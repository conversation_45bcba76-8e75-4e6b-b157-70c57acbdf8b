/* Import Google Fonts - TWL Typography System */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700;800;900&family=Cinzel:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* TWL CSS Variables - Hype Luxury Footwear Color System (2025) */
:root {
  /* Hype Luxury Footwear Core Colors */
  --color-primary: #BFFF00;        /* Neon Volt Lime */
  --color-primary-dark: #A6E600;   /* Neon Volt Lime (Dark) */
  --color-secondary: #B0B0B0;      /* Chrome Metallic */
  --color-secondary-dark: #9A9A9A; /* Chrome Metallic (Dark) */
  --color-accent: #D9D9D9;         /* Platinum Silver */
  --color-accent-dark: #C3C3C3;    /* Platinum Silver (Dark) */

  /* Monochrome Base Colors */
  --color-ice-white: #F9FAFB;      /* Ice White */
  --color-platinum-silver: #D9D9D9; /* Platinum Silver */
  --color-jet-black: #0B0B0B;      /* Jet Black */
  --color-graphite-gray: #2A2A2A;  /* Graphite Gray */
  --color-chrome-metallic: #B0B0B0; /* Chrome Metallic */

  /* Background Colors */
  --color-background-light: #F9FAFB;  /* Ice White */
  --color-background-dark: #1A1A1A;   /* Dark Grey (softer than jet black) */
  --color-background-overlay: #2A2A2A; /* Graphite Gray */

  /* Text Colors */
  --color-text-primary: #0B0B0B;     /* Jet Black */
  --color-text-secondary: #2A2A2A;   /* Graphite Gray */
  --color-text-tertiary: #B0B0B0;    /* Chrome Metallic */
  --color-text-inverse: #F9FAFB;     /* Ice White */

  /* Border Colors */
  --color-border-default: #D9D9D9;   /* Platinum Silver */
  --color-border-light: #B0B0B0;     /* Chrome Metallic */
  --color-border-dark: #2A2A2A;      /* Graphite Gray */

  /* Glassmorphism Overlays */
  --color-frosted-light: rgba(249,250,251,0.08);
  --color-frosted-dark: rgba(26,26,26,0.08);
  --color-frosted-overlay: rgba(217,217,217,0.15);

  /* Legacy Status Colors (keeping for existing components) */
  --color-success: #28C76F;
  --color-success-dark: #24B364;
  --color-error: #FF4C51;
  --color-error-dark: #E64449;
  --color-warning: #FFB800;
  --color-warning-dark: #E6A600;
  --color-info: #00BAD1;
  --color-info-dark: #00A7BC;
}

/* Dark mode color scheme */
.dark {
  color-scheme: dark;
}

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-feature-settings: 'rlig' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar - Hype Luxury Footwear Style */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--color-background-light);
}

.dark ::-webkit-scrollbar-track {
  background-color: var(--color-background-dark);
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 9999px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: var(--color-chrome-metallic);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-accent);
}

/* TWL Glassmorphism & Utility Classes */
@layer utilities {
  .glass {
    @apply backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10;
  }

  .glass-card {
    @apply glass rounded-xl shadow-glass;
  }

  .glass-button {
    @apply glass rounded-lg transition-all duration-300 hover:bg-white/20 dark:hover:bg-white/10;
  }

  /* Hype Luxury Footwear Brand Color Glows */
  .primary-glow {
    box-shadow: 0 0 20px rgba(191, 255, 0, 0.5);
  }

  .neon-glow {
    box-shadow: 0 0 20px rgba(191, 255, 0, 0.5);
  }

  .chrome-glow {
    box-shadow: 0 0 20px rgba(176, 176, 176, 0.3);
  }

  .platinum-glow {
    box-shadow: 0 0 15px rgba(217, 217, 217, 0.4);
  }

  .success-glow {
    box-shadow: 0 0 20px rgba(40, 199, 111, 0.5);
  }

  .error-glow {
    box-shadow: 0 0 20px rgba(255, 76, 81, 0.5);
  }

  .info-glow {
    box-shadow: 0 0 20px rgba(0, 186, 209, 0.5);
  }
}

/* TWL Component Styles - Hype Luxury Footwear (2025) */
@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-jet-black font-montserrat font-medium py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-neon-glow hover:scale-105 active:scale-95;
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary-dark text-ice-white font-montserrat font-medium py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-chrome-glow hover:scale-105 active:scale-95;
  }

  .btn-accent {
    @apply bg-accent hover:bg-accent-dark text-jet-black font-montserrat font-medium py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-platinum-glow hover:scale-105 active:scale-95;
  }

  .btn-ghost {
    @apply glass-button text-text-secondary dark:text-text-inverse font-montserrat font-medium py-3 px-6;
  }

  .btn-success {
    @apply bg-success hover:bg-success-dark text-white font-montserrat font-medium py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-success-glow hover:scale-105 active:scale-95;
  }

  .btn-error {
    @apply bg-error hover:bg-error-dark text-white font-montserrat font-medium py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-error-glow hover:scale-105 active:scale-95;
  }

  .input-glass {
    @apply glass rounded-lg px-4 py-3 text-text-primary dark:text-text-inverse placeholder:text-text-tertiary dark:placeholder:text-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 font-inter;
  }

  .card-glass {
    @apply glass-card p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1;
  }

  /* Typography Classes - Hype Luxury Footwear */
  .heading-1 {
    @apply text-5xl font-montserrat font-bold text-text-primary dark:text-text-inverse;
  }

  .heading-2 {
    @apply text-4xl font-montserrat font-bold text-text-primary dark:text-text-inverse;
  }

  .heading-3 {
    @apply text-2xl font-montserrat font-semibold text-text-primary dark:text-text-inverse;
  }

  .heading-4 {
    @apply text-xl font-montserrat font-medium text-text-primary dark:text-text-inverse;
  }

  .body-1 {
    @apply text-base font-inter font-normal text-text-secondary dark:text-text-tertiary;
  }

  .body-2 {
    @apply text-sm font-inter font-normal text-text-tertiary dark:text-text-tertiary;
  }

  .caption {
    @apply text-xs font-inter font-normal text-text-tertiary dark:text-text-tertiary;
  }
}

/* Animation classes */
@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 3s infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
    background: rgba(255, 255, 255, 0.1);
  }

  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }

  .animate-emerald-glow-pulse {
    animation: emerald-glow-pulse 3s ease-in-out infinite;
  }

  .animate-breathe {
    animation: breathe 4s ease-in-out infinite;
  }

  .animate-morph {
    animation: morph 6s ease-in-out infinite;
  }

  .animate-drift {
    animation: drift 10s ease-in-out infinite;
  }

  .animate-cascade {
    animation: cascade 1.5s ease-out;
  }

  .animate-emerge {
    animation: emerge 2s ease-out;
  }

  .animate-ripple {
    animation: ripple 2s ease-out;
  }

  .animate-text-shimmer {
    animation: text-shimmer 3s linear infinite;
    color: var(--color-primary);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(191, 255, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(191, 255, 0, 0.8), 0 0 60px rgba(191, 255, 0, 0.4);
  }
}

@keyframes neon-glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(191, 255, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(191, 255, 0, 0.8), 0 0 60px rgba(191, 255, 0, 0.4);
  }
}

@keyframes chrome-glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(176, 176, 176, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(176, 176, 176, 0.5), 0 0 40px rgba(176, 176, 176, 0.3);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Focus styles for accessibility - Hype Luxury Footwear Style */
.focus-visible:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px var(--color-background-light);
}

.dark .focus-visible:focus {
  box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px var(--color-background-dark);
}

/* Selection styles - Hype Luxury Footwear Style */
::selection {
  background-color: var(--color-primary);
  color: var(--color-jet-black);
}

::-moz-selection {
  background-color: var(--color-primary);
  color: var(--color-jet-black);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Product Card Sizing Utilities */
@layer utilities {
  .product-card-container {
    @apply h-full flex flex-col;
  }

  .product-card-image {
    @apply aspect-square flex-shrink-0 overflow-hidden;
  }

  .product-card-content {
    @apply flex-1 flex flex-col justify-between p-3;
  }

  .product-card-title {
    @apply min-h-[50px] flex flex-col justify-start;
  }

  .product-card-price {
    @apply mt-auto;
  }

  /* Equal height grid items */
  .equal-height-grid {
    @apply grid auto-rows-fr;
  }

  /* Enterprise-Grade Responsive Product Grid System */
  /* Mobile-First Approach with Specific Column Constraints */
  /* TWL Luxury E-commerce Grid System - 2025 Standards */

  .product-grid-mobile {
    @apply grid grid-cols-2 gap-3 auto-rows-fr;
    /* Mobile-optimized grid for touch interfaces */
  }

  .product-grid-desktop {
    @apply grid auto-rows-fr;
    /* Enhanced Desktop Grid: Mobile: 2 cols, Tablet: 3 cols, Desktop: 4 cols, Large: 5-6 cols */
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem; /* 16px - Mobile optimized */
    transition: gap 0.3s ease;
  }

  .product-grid-desktop-with-sidebar {
    @apply grid auto-rows-fr;
    /* Sidebar-aware Grid: Mobile: 2 cols, Tablet: 2 cols, Desktop: 3 cols, Large: 4 cols */
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem; /* 12px - Compact for sidebar */
    transition: gap 0.3s ease;
  }

  /* Enhanced Responsive Breakpoints for Desktop Grid */
  /* SM: Tablet Portrait (640px+) - 3 columns for better tablet experience */
  @media (min-width: 640px) {
    .product-grid-desktop {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.25rem; /* 20px - Tablet optimized */
    }
    .product-grid-desktop-with-sidebar {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem; /* 16px - Sidebar tablet */
    }
  }

  /* MD: Tablet Landscape (768px+) - Enhanced tablet experience */
  @media (min-width: 768px) {
    .product-grid-desktop {
      gap: 1.5rem; /* 24px - Better spacing for larger tablets */
    }
    .product-grid-desktop-with-sidebar {
      gap: 1.25rem; /* 20px - Sidebar tablet landscape */
    }
  }

  /* LG: Desktop (1024px+) - 4 columns as per requirement */
  @media (min-width: 1024px) {
    .product-grid-desktop {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem; /* 32px - Desktop optimized */
    }
    .product-grid-desktop-with-sidebar {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem; /* 24px - Sidebar desktop */
    }
  }

  /* XL: Large Desktop (1280px+) - 5 columns for large screens */
  @media (min-width: 1280px) {
    .product-grid-desktop {
      grid-template-columns: repeat(5, 1fr);
      gap: 2rem; /* 32px - Large desktop */
    }
    .product-grid-desktop-with-sidebar {
      grid-template-columns: repeat(4, 1fr);
      gap: 1.5rem; /* 24px - Sidebar large desktop */
    }
  }

  /* 2XL: Extra Large Desktop (1536px+) - 6 columns for ultra-wide */
  @media (min-width: 1536px) {
    .product-grid-desktop {
      grid-template-columns: repeat(6, 1fr);
      gap: 2.5rem; /* 40px - Ultra-wide optimized */
    }
    .product-grid-desktop-with-sidebar {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem; /* 32px - Sidebar ultra-wide */
    }
  }

  /* Advanced Grid Features */

  /* Container-aware grid for future container queries support */
  .product-grid-container {
    container-type: inline-size;
    container-name: product-grid;
  }

  /* Grid debugging utilities (development mode) */
  .grid-debug {
    position: relative;
  }

  .grid-debug::before {
    content: attr(data-cols) " cols";
    position: absolute;
    top: -1.5rem;
    left: 0;
    font-size: 0.75rem;
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    z-index: 10;
    pointer-events: none;
  }

  /* Enhanced product card constraints for consistent sizing */
  .product-grid-desktop > *,
  .product-grid-desktop-with-sidebar > * {
    min-height: 320px; /* Minimum card height */
    max-height: 480px; /* Maximum card height */
  }

  /* Responsive aspect ratio maintenance */
  .product-grid-desktop .product-card-image,
  .product-grid-desktop-with-sidebar .product-card-image {
    aspect-ratio: 1 / 1.2; /* Consistent aspect ratio */
    object-fit: cover;
  }

  /* Legacy responsive grid for backward compatibility */
  .product-grid-responsive {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Grid performance optimizations */
  .product-grid-desktop,
  .product-grid-desktop-with-sidebar {
    will-change: grid-template-columns, gap;
    contain: layout style;
  }

  /* Custom Scrollbar for Filter Panel */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(191, 255, 0, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(191, 255, 0, 0.3);
    border-radius: 2px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(191, 255, 0, 0.5);
  }
}

/* Mobile-first responsive design */
@media (max-width: 640px) {
  .mobile-grid-1 { @apply grid-cols-1; }
  .mobile-grid-2 { @apply grid-cols-2; }
  .mobile-text-sm { @apply text-sm; }
  .mobile-text-xs { @apply text-xs; }
  .mobile-p-2 { @apply p-2; }
  .mobile-p-3 { @apply p-3; }
  .mobile-gap-2 { @apply gap-2; }
  .mobile-gap-3 { @apply gap-3; }

  /* Mobile product cards */
  .product-card-mobile {
    @apply h-full min-h-[320px];
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }

  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  button, a, [role="button"] {
    @apply touch-target;
  }
}

/* Safe area for iOS devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.h-safe-area-inset-bottom {
  height: env(safe-area-inset-bottom);
}

/* Mobile-optimized scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Mobile-specific glassmorphism */
@media (max-width: 1024px) {
  .glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background: rgba(26, 26, 26, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Mobile navigation enhancements */
.mobile-nav-item {
  @apply flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-200;
  min-width: 60px;
  min-height: 60px;
}

.mobile-nav-item:active {
  @apply scale-95;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
