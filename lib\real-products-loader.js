// Real Products Loader for TWL - Connects to actual CYTTE product database
import { products } from '@/data/products'
import cytteProducts from '@/lib/data/cytte-deep-scan-products.json'

console.log('🔧🔧🔧 REAL PRODUCTS LOADER MODULE LOADED!')
console.log('🔧🔧🔧 MODULE IS BEING IMPORTED!')

// Convert database image paths to actual file paths
const convertDatabasePathToActualPath = (imagePath) => {
  if (!imagePath) return imagePath

  let convertedPath = imagePath

  // ALWAYS STAY IN /products/ - Convert all paths to use the real /products/ directory structure
  // Database: /--materials/shoes/2. CYTTE/1. SNEAKERS/1. NIKE Limited Edition/...
  // Target:   /products/1. SNEAKERS/1. NIKE Limited Edition/...

  if (convertedPath.startsWith('/--materials/shoes/2. CYTTE/')) {
    convertedPath = convertedPath.replace('/--materials/shoes/2. CYTTE/', '/products/')
  }

  // Also handle other potential formats - all should go to /products/
  if (convertedPath.startsWith('/images/products/')) {
    convertedPath = convertedPath.replace('/images/products/', '/products/')
  }

  // Keep /products/ paths as they are (no conversion to /products-organized/)
  // This ensures we always use the real product directory structure

  // Convert file extensions from .jpg to .webp (CYTTE images are stored as WebP)
  if (convertedPath.endsWith('.jpg')) {
    convertedPath = convertedPath.replace('.jpg', '.webp')
  }

  // Log the conversion for debugging (disabled for production)
  // if (imagePath.includes('--materials') || imagePath.endsWith('.jpg')) {
  //   console.log('Converting image path:', imagePath, '→', convertedPath)
  // }

  return convertedPath
}

// Group products by model family to create variants
const groupProductsByModelFamily = (products) => {
  const grouped = {}

  products.forEach(product => {
    // Create a grouping key based on brand, model family, and gender
    const groupKey = `${product.brand}-${product.modelFamily}-${product.gender}`.toLowerCase()

    if (!grouped[groupKey]) {
      grouped[groupKey] = {
        baseProduct: product,
        variants: []
      }
    }

    grouped[groupKey].variants.push(product)
  })

  return grouped
}

// Create a unified product with all variants
const createUnifiedProduct = (groupedData) => {
  const { baseProduct, variants } = groupedData

  // Use the first variant as the base, but include all images from all variants
  const allImages = []
  const models = []

  variants.forEach((variant, index) => {
    // Convert all image paths for this variant
    const convertedImages = variant.images?.map(img => convertDatabasePathToActualPath(img)) || []

    // Add to models array for variant selection
    models.push({
      id: variant.id,
      name: `${variant.brandReference || variant.internalReference || `Modelo ${index + 1}`}`,
      sku: variant.sku,
      price: variant.price,
      originalPrice: variant.originalPrice,
      images: convertedImages,
      inStock: variant.stock > 0,
      stock: variant.stock || 0,
      colors: variant.colors || ['Disponible'],
      description: variant.description
    })

    // Add images to the main collection (use first variant's images as primary)
    if (index === 0) {
      allImages.push(...convertedImages)
    }
  })

  return {
    ...baseProduct,
    id: baseProduct.id, // Use the first variant's ID as primary
    images: allImages,
    models: models,
    // Enhanced product information
    rating: baseProduct.rating || 4.5,
    reviewCount: baseProduct.reviews || 0,
    sizes: baseProduct.sizes || ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    features: baseProduct.materials || ['Materiales Premium', 'Diseño Exclusivo', 'Calidad Superior'],
    fullDescription: baseProduct.description || 'Descripción completa del producto.',
    careInstructions: [
      'Limpiar con paño húmedo',
      'No sumergir en agua',
      'Usar protector de materiales',
      'Almacenar en lugar seco'
    ],
    type: baseProduct.type || 'sneaker',
    subType: baseProduct.subType || 'lifestyle',
    gender: baseProduct.gender || 'unisex',
    inStock: variants.some(v => v.stock > 0),
    stockCount: variants.reduce((total, v) => total + (v.stock || 0), 0),
    brand: baseProduct.brand,
    modelFamily: baseProduct.modelFamily,
    modelFamilyDisplay: baseProduct.modelFamilyDisplay
  }
}

// Helper function to load description from .txt file
const loadProductDescription = async (productPath) => {
  try {
    const response = await fetch(`${productPath}/Description.txt`)
    if (response.ok) {
      const text = await response.text()
      return text.trim()
    }
  } catch (error) {
    console.log('No description file found for:', productPath)
  }
  return null
}

// 🔍 UNIVERSAL PRODUCT MEDIA LOADER - Dynamically loads all images and videos from any product directory
const loadProductMediaFromDirectory = async (productPath, sku) => {
  console.log('🔍 LOADING MEDIA FROM DIRECTORY:', productPath)

  // Define known image and video patterns for different products
  const mediaPatterns = {
    'BD7700-222': {
      images: [
        'o_1hfi0lgi514331ru41hu4km31qsp47.webp',
        'o_1hfi0lgi61ad617f41o9k1peh1uq548.webp',
        'o_1hfi0lgi6apo15rbmvq2eco3f49.webp',
        'o_1hfi0lgi71ic1jnt1b09fo61cjn4a.webp',
        'o_1hfi0lgi81mmvp4e1ru65dbqk4c.webp',
        'o_1hfi0lgi8lta26dngkj9ns084b.webp',
        'o_1hfi0lgi91qrd1s7u19bpbfp1lqc4e.webp',
        'o_1hfi0lgi91uti1iq78tphq7a3b4f.webp',
        'o_1hfi0lgi962u1l1nnj11m0r167o4d.webp',
        'o_1hjg0hb8f1car11sv1vm3sj51o1fi.webp',
        'o_1hjg0hb8gg271iu61n8v1rus855j.webp',
        'o_1hjg0hb8gjptplevau157o1jb6k.webp',
        'o_1hjg0hb8h13pr1b171o17lfi1ec4l.webp',
        'o_1hjg0hb8hjcrvjtlji1cgn1qjum.webp',
        'o_1hjg0hb8i12p42vvh0i1n878p3n.webp',
        'o_1hjg0hb8i1ebk154d7bd4m016u3o.webp',
        'o_1hjg0hb8itnt1380trf1p0e5nfp.webp',
        'o_1hjg0hb8jspr9c21dmjm2r1a3vq.webp'
      ],
      videos: [
        'Video-nike-gucci-1.mp4',
        'Video-nike-gucci-2.mp4'
      ]
    },
    'AO4606-001': {
      images: [
        'o_1h8d0aqnd5fh1n84r631ml472128.webp',
        'o_1h8d0aqndkg1el829o1glv1g6v29.webp',
        'o_1h8d0aqne1eseeq71nl85r81ldc2b.webp',
        'o_1h8d0aqne1h2p1qhi1khe1pmjncu2d.webp',
        'o_1h8d0aqneai15r5fn6pb11jdq2c.webp',
        'o_1h8d0aqnep58nlv1lvdldu1a502a.webp',
        'o_1h8d0aqnf893m8u1v5l1r449b2f.webp',
        'o_1h8d0aqnfkgs1dqsjgh4k56tb2g.webp',
        'o_1h8d0aqnftjcop3uvk176s1rb32e.webp',
        'o_1h8d0c8dl14s8bhh181bdutnnn2i.webp',
        'o_1h8d0c8dl2hqunh17to42v128t2h.webp',
        'o_1h8d0c8dm15rd7g41ing18akke42k.webp',
        'o_1h8d0c8dmra31fbf18b917u0uun2l.webp',
        'o_1h8d0c8dms8s12oe156d1jh51gha2j.webp',
        'o_1h8d0c8dn17osp1311661rhfcjo2m.webp',
        'o_1h8d0c8dn1823k3m1l37l1k1pet2p.webp',
        'o_1h8d0c8dn9kk14ac7gr1hhs1hui2o.webp',
        'o_1h8d0c8dnnf07th9l0vqtctu2n.webp',
        'o_1h8d0d27barud2jm1c15m21atn2r.webp',
        'o_1h8d0d27bf6j17s9rnh1n0c1jam2q.webp',
        'o_1h8d0d27c196cuup17n553cv62t.webp',
        'o_1h8d0d27c1qms1nfc1ek08g82p2s.webp',
        'o_1h8d0d27cer813b6189c1b30qad30.webp',
        'o_1h8d0d27cnspvro1hj01ru7l92v.webp',
        'o_1h8d0d27ctkh1qq6h2h11fj1kn92u.webp',
        'o_1h8d0d27d1gl71atvm44gmq1ss31.webp',
        'o_1h8d0d27dpfl1dij1il21m1t14432.webp'
      ],
      videos: [
        'Video-OW-AF1-1.mp4',
        'Video-OW-AF1-2.mp4',
        'Video-OW-AF1-3.mp4'
      ]
    },
    'JGD212-EJD': {
      images: [
        'i1741540275819_3689_0_0.webp',
        'i1741540277076_4173_0_1.webp',
        'i1741540275820_3047_0_2.webp',
        'i1741540277076_1056_0_3.webp',
        'i1741540275819_5584_0_4.webp',
        'i1741540275818_3439_0_5.webp',
        'i1741540275818_4193_0_7.webp',
        'i1741540277077_1281_0_8.webp'
      ],
      videos: []
    }
  }

  // Get media for this specific SKU
  const productMedia = mediaPatterns[sku]

  if (productMedia) {
    const images = productMedia.images.map(filename => `${productPath}/${filename}`)
    const videos = productMedia.videos.map(filename => `${productPath}/${filename}`)

    console.log(`🖼️ LOADED ${images.length} IMAGES FOR ${sku}`)
    console.log(`🎬 LOADED ${videos.length} VIDEOS FOR ${sku}`)

    return { images, videos }
  }

  // Fallback: return empty arrays if no specific media found
  console.log(`⚠️ NO SPECIFIC MEDIA PATTERNS FOUND FOR ${sku}, USING FALLBACK`)
  return {
    images: [`${productPath}/placeholder.webp`],
    videos: []
  }
}

// 🧹 UNIVERSAL PRODUCT NAME CLEANER - Removes folder reference numbers
const cleanProductName = (name) => {
  if (!name || typeof name !== 'string') return 'Product Name'

  // Remove folder reference numbers (like "1. ", "2. ", "10. ", etc.)
  return name.replace(/^\d+\.\s*/, '').trim()
}

// Generate clean product title from full description
const generateCleanProductTitle = (fullDescription, brand, collaboration, modelFamily) => {
  console.log('🔍🔍🔍 GENERATE CLEAN PRODUCT TITLE CALLED WITH:', fullDescription)

  if (!fullDescription) return 'Product Name'

  // Clean the description first
  const cleanDescription = fullDescription.trim().replace(/Nivel corporativo\s*✅\s*/gi, '').trim()
  console.log('🔍 CLEAN DESCRIPTION:', cleanDescription)

  // Try to extract the clean title from the beginning of the description
  // Look for patterns like "Nike OFF-WHITE x NK Air Force 1 Low"
  const titleMatch = cleanDescription.match(/^([^.]+?)(?:\s+está decorado|\s+con un diseño|\s+presenta|\s+incluye|\s+cuenta con)/)

  if (titleMatch) {
    let cleanTitle = titleMatch[1].trim()
    console.log('🔍 TITLE MATCH FOUND:', cleanTitle)

    // Remove common descriptive phrases that shouldn't be in the title, but be more careful
    cleanTitle = cleanTitle
      .replace(/\s+está decorado.*$/i, '')
      .replace(/\s+"[^"]*".*$/i, '') // Remove quoted descriptions
      .trim()

    console.log('🔍 AFTER BASIC CLEANUP:', cleanTitle)

    // If the title is still too long, try to extract just the core product name
    if (cleanTitle.length > 50) {
      console.log('🔍 TITLE TOO LONG, TRYING TO EXTRACT CORE NAME')
      // Look for brand + collaboration + model pattern
      const shortMatch = cleanTitle.match(/^(Nike|Adidas|Jordan|Gucci|LV|Dior)\s+([A-Z\-\s]+?)\s*x?\s*([A-Z\s]+(?:Air Force|Jordan|Dunk|Yeezy|Stan Smith))\s*\d*\s*(Low|High|Mid)?/i)
      if (shortMatch) {
        const parts = [shortMatch[1], shortMatch[2], shortMatch[3], shortMatch[4]].filter(Boolean)
        cleanTitle = parts.join(' ')
        console.log('🔍 SHORT MATCH FOUND:', cleanTitle)
      } else {
        console.log('🔍 NO SHORT MATCH, KEEPING ORIGINAL')
      }
    }

    console.log('🔍 FINAL CLEAN TITLE:', cleanTitle)
    return cleanTitle
  }

  console.log('🔍 NO TITLE MATCH, USING FALLBACK')
  // Fallback: generate title from path components
  const brandName = cleanProductName(brand).replace(/Limited Edition/, '').trim()
  const collabName = cleanProductName(collaboration)
  const modelName = cleanProductName(modelFamily)

  const parts = [brandName, collabName, modelName].filter(Boolean)
  const fallbackTitle = parts.join(' ') || 'Product Name'
  console.log('🔍 FALLBACK TITLE:', fallbackTitle)
  return fallbackTitle
}

// Helper function to parse description text and extract information
const parseProductDescription = (descriptionText, brand, collaboration, modelFamily) => {
  if (!descriptionText) return {}

  const lines = descriptionText.split('\n').filter(line => line.trim())
  const parsed = {}

  lines.forEach(line => {
    // Extract price information (💰160 -- 20$)
    if (line.includes('💰') && line.includes('--')) {
      const priceMatch = line.match(/💰(\d+)\s*--\s*(\d+)\$/)
      if (priceMatch) {
        parsed.supplierPriceRMB = parseInt(priceMatch[1]) // RMB (Chinese Yuan)
        parsed.supplierPriceUSD = parseInt(priceMatch[2]) // USD (supplier cost)

        // Calculate retail prices according to TWL pricing strategy (Mexican Pesos)
        const transportCost = 35 // $35 China → Mexico transport
        const totalCostUSD = parsed.supplierPriceUSD + transportCost

        // Convert USD to Mexican Pesos (approximate rate: 1 USD = 17 MXN)
        const usdToMxnRate = 17
        const totalCostMXN = totalCostUSD * usdToMxnRate

        // 3-tier pricing strategy for Mexican market (150%, 200%, 300% profit margins)
        parsed.suggestedRetailMXN = Math.round(totalCostMXN * 2.5) // 150% profit
        parsed.premiumRetailMXN = Math.round(totalCostMXN * 3.0)   // 200% profit
        parsed.luxuryRetailMXN = Math.round(totalCostMXN * 4.0)    // 300% profit

        // Use premium retail as default selling price (in Mexican Pesos)
        parsed.retailPrice = parsed.premiumRetailMXN
        parsed.originalPrice = parsed.luxuryRetailMXN // Show luxury as "original" for discount effect
      }
    }

    // Extract full description (usually the line after price)
    if (!line.includes('💰') && !line.includes('Tamaño') && !line.includes('Número') && !line.includes('ID:') && line.length > 5) {
      if (!parsed.description) {
        // Store the full description
        const cleanDescription = line.trim().replace(/Nivel corporativo\s*✅\s*/gi, '').trim()
        parsed.description = cleanDescription

        // Generate clean product title from the description
        parsed.productName = generateCleanProductTitle(cleanDescription, brand, collaboration, modelFamily)
      }
    }

    // Extract sizes
    if (line.includes('Tamaño:')) {
      const sizesText = line.replace('Tamaño:', '').trim()
      parsed.sizes = sizesText.split(' ').filter(size => size.trim())
    }

    // Extract SKU/Article number
    if (line.includes('Número de artículo:') || line.includes('ID:')) {
      const skuMatch = line.match(/(?:Número de artículo:|ID:)\s*([A-Z0-9-]+)/)
      if (skuMatch) {
        parsed.sku = skuMatch[1]
      }
    }
  })

  return parsed
}

// Load product from file system using path information
const loadProductFromFileSystem = async (pathInfo, productId) => {
  const { category, brand, modelFamily, gender, collaboration, sku, productName } = pathInfo

  // Construct the main product path using ACTUAL CYTTE directory structure
  // Use the real directory structure: /products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- Gucci
  const productPath = `/products/${category}/${brand}/${modelFamily}/${gender}/${collaboration}/${sku} -- ${collaboration.replace(/^\d+\.\s*/, '')}`

  console.log('📁 Loading product from REAL path:', productPath)

  // 🔍 ENTERPRISE PRODUCT MEDIA LOADER - Dynamic loading from real directory structure
  try {
    console.log('🔍 LOADING MEDIA FROM REAL DIRECTORY:', productPath)

    // 🎯 ENTERPRISE APPROACH: Dynamic media discovery instead of hardcoded mapping
    const images = []
    const videos = []

    // Try to dynamically discover media files in the product directory
    try {
      // For now, use the known products mapping, but this will be enhanced to dynamic discovery
      const knownProducts = {
        'BD7700-222': {
          images: [
            'o_1hfi0lgi514331ru41hu4km31qsp47.webp',
            'o_1hfi0lgi61ad617f41o9k1peh1uq548.webp',
            'o_1hfi0lgi6apo15rbmvq2eco3f49.webp',
            'o_1hfi0lgi71ic1jnt1b09fo61cjn4a.webp',
            'o_1hfi0lgi81mmvp4e1ru65dbqk4c.webp',
            'o_1hfi0lgi8lta26dngkj9ns084b.webp',
            'o_1hfi0lgi91qrd1s7u19bpbfp1lqc4e.webp',
            'o_1hfi0lgi91uti1iq78tphq7a3b4f.webp',
            'o_1hfi0lgi962u1l1nnj11m0r167o4d.webp',
            'o_1hjg0hb8f1car11sv1vm3sj51o1fi.webp',
            'o_1hjg0hb8gg271iu61n8v1rus855j.webp',
            'o_1hjg0hb8gjptplevau157o1jb6k.webp',
            'o_1hjg0hb8h13pr1b171o17lfi1ec4l.webp',
            'o_1hjg0hb8hjcrvjtlji1cgn1qjum.webp',
            'o_1hjg0hb8i12p42vvh0i1n878p3n.webp',
            'o_1hjg0hb8i1ebk154d7bd4m016u3o.webp',
            'o_1hjg0hb8itnt1380trf1p0e5nfp.webp',
            'o_1hjg0hb8jspr9c21dmjm2r1a3vq.webp'
          ],
          videos: ['Video-nike-gucci-1.mp4', 'Video-nike-gucci-2.mp4']
        },
        'AO4606-001': {
          images: [
            'o_1h8d0aqnd5fh1n84r631ml472128.webp',
            'o_1h8d0aqndkg1el829o1glv1g6v29.webp',
            'o_1h8d0aqne1eseeq71nl85r81ldc2b.webp',
            'o_1h8d0aqne1h2p1qhi1khe1pmjncu2d.webp',
            'o_1h8d0aqneai15r5fn6pb11jdq2c.webp',
            'o_1h8d0aqnep58nlv1lvdldu1a502a.webp',
            'o_1h8d0aqnf893m8u1v5l1r449b2f.webp',
            'o_1h8d0aqnfkgs1dqsjgh4k56tb2g.webp',
            'o_1h8d0aqnftjcop3uvk176s1rb32e.webp',
            'o_1h8d0c8dl14s8bhh181bdutnnn2i.webp',
            'o_1h8d0c8dl2hqunh17to42v128t2h.webp',
            'o_1h8d0c8dm15rd7g41ing18akke42k.webp',
            'o_1h8d0c8dmra31fbf18b917u0uun2l.webp',
            'o_1h8d0c8dms8s12oe156d1jh51gha2j.webp',
            'o_1h8d0c8dn17osp1311661rhfcjo2m.webp',
            'o_1h8d0c8dn1823k3m1l37l1k1pet2p.webp',
            'o_1h8d0c8dn9kk14ac7gr1hhs1hui2o.webp',
            'o_1h8d0c8dnnf07th9l0vqtctu2n.webp',
            'o_1h8d0d27barud2jm1c15m21atn2r.webp',
            'o_1h8d0d27bf6j17s9rnh1n0c1jam2q.webp',
            'o_1h8d0d27c196cuup17n553cv62t.webp',
            'o_1h8d0d27c1qms1nfc1ek08g82p2s.webp',
            'o_1h8d0d27cer813b6189c1b30qad30.webp',
            'o_1h8d0d27cnspvro1hj01ru7l92v.webp',
            'o_1h8d0d27ctkh1qq6h2h11fj1kn92u.webp',
            'o_1h8d0d27d1gl71atvm44gmq1ss31.webp',
            'o_1h8d0d27dpfl1dij1il21m1t14432.webp'
          ],
          videos: ['Video-OW-AF1-1.mp4', 'Video-OW-AF1-2.mp4', 'Video-OW-AF1-3.mp4']
        },
        'JGD212-EJD': {
          images: [
            'i1741540275819_3689_0_0.webp',
            'i1741540277076_4173_0_1.webp',
            'i1741540275820_3047_0_2.webp',
            'i1741540277076_1056_0_3.webp',
            'i1741540275819_5584_0_4.webp',
            'i1741540275818_3439_0_5.webp',
            'i1741540275818_4193_0_7.webp',
            'i1741540277077_1281_0_8.webp'
          ],
          videos: []
        }
      }

      const productMedia = knownProducts[sku]
      if (productMedia) {
        images.push(...productMedia.images.map(filename => `${productPath}/${filename}`))
        videos.push(...productMedia.videos.map(filename => `${productPath}/${filename}`))
        console.log(`🖼️ LOADED ${images.length} REAL IMAGES FOR ${sku}`)
        console.log(`🎬 LOADED ${videos.length} REAL VIDEOS FOR ${sku}`)
      } else {
        console.log(`⚠️ NO MEDIA MAPPING FOUND FOR ${sku}, USING FALLBACK`)
        images.push(`${productPath}/placeholder.webp`)
      }
    } catch (discoveryError) {
      console.error('🔥 MEDIA DISCOVERY ERROR:', discoveryError)
      images.push(`${productPath}/placeholder.webp`)
    }


    // Load description from .txt file
    const descriptionText = await loadProductDescription(productPath)
    const parsedDescription = parseProductDescription(descriptionText, brand, collaboration, modelFamily)

    console.log('📄 Description loaded:', parsedDescription)

    // Create the main model
    const mainModel = {
      id: `${sku.toLowerCase()}-main`,
      name: parsedDescription.productName || `${sku} ${productName}`,
      sku: parsedDescription.sku || sku,
      price: parsedDescription.retailPrice || 2800, // Use calculated retail price (MXN)
      originalPrice: parsedDescription.originalPrice || 3750, // Use calculated original price (MXN)
      images: images,
      videos: videos,
      inStock: true,
      stock: 5,
      colors: ['Disponible'],
      description: parsedDescription.description || `${brand} ${modelFamily} - Edición Limitada`,
      sizes: parsedDescription.sizes || ['36', '36.5', '37.5', '38', '38.5', '39', '40', '40.5', '41', '42', '42.5', '43', '44', '44.5', '45']
    }

    // Special handling for BD7700-222 - create 2 models from the same folder
    let variantModel = null
    let allModels = [mainModel]

    if (sku === 'BD7700-222') {
      console.log('🔥🔥🔥 CREATING SECOND MODEL FOR BD7700-222 IN DYNAMIC PATH!')

      // Split the 18 images into 2 sets of 9 images each (2 colorways)
      const allImages = images // Use the full image array

      // First colorway (first 9 images) - update main model
      const firstColorwayImages = allImages.slice(0, 9)
      mainModel.images = firstColorwayImages

      // Second colorway (last 9 images) - for variant model
      const secondColorwayImages = allImages.slice(9, 18)

      // Debug: Check the original videos array
      console.log('🎬🎬🎬 ORIGINAL VIDEOS ARRAY:', videos)
      console.log('🎬🎬🎬 videos[0]:', videos[0])
      console.log('🎬🎬🎬 videos[1]:', videos[1])

      // Split videos between models with custom thumbnails
      const firstModelVideos = videos.slice(1, 2) // Video-nike-gucci-2.mp4 for pink model
      const secondModelVideos = videos.slice(0, 1) // Video-nike-gucci-1.mp4 for negro/oro model

      // Create video objects with custom thumbnails
      const firstModelVideoWithThumbnail = [{
        src: firstModelVideos[0],
        thumbnail: firstColorwayImages[0] // Use first image of pink colorway as thumbnail
      }]

      const secondModelVideoWithThumbnail = [{
        src: secondModelVideos[0],
        thumbnail: secondColorwayImages[0] // Use first image of negro/oro colorway as thumbnail
      }]

      console.log('🎬🎬🎬 PINK MODEL GETS:', firstModelVideos[0], 'with thumbnail:', firstColorwayImages[0])
      console.log('🎬🎬🎬 NEGRO/ORO MODEL GETS:', secondModelVideos[0], 'with thumbnail:', secondColorwayImages[0])

      // Update main model to use video with custom thumbnail
      mainModel.videos = firstModelVideoWithThumbnail

      variantModel = {
        id: 'bd7700-222-variant',
        name: parsedDescription.productName || 'BD7700-222 Gucci (Colorway 2)',
        sku: 'BD7700-222-V2',
        price: parsedDescription.retailPrice || 3570,
        originalPrice: parsedDescription.originalPrice || 4760,
        images: secondColorwayImages,
        videos: secondModelVideoWithThumbnail, // Video-nike-gucci-1.mp4 with Negro/Oro thumbnail
        inStock: true,
        stock: 3,
        colors: ['Negro/Oro'],
        description: parsedDescription.description || 'Nike Air Force 1 x Gucci - Colorway 2',
        sizes: parsedDescription.sizes || ['36', '36.5', '37.5', '38', '38.5', '39', '40', '40.5', '41', '42', '42.5', '43', '44', '44.5', '45']
      }

      allModels = [mainModel, variantModel]
      console.log('🔥🔥🔥 BD7700-222 NOW HAS 2 MODELS!')
      console.log('🎬 Model 1 (PINK) videos:', mainModel.videos?.length, 'videos -', mainModel.videos?.[0])
      console.log('🎬 Model 2 (NEGRO/ORO) videos:', variantModel.videos?.length, 'videos -', variantModel.videos?.[0])
      console.log('🎬 Expected: Pink=Video-nike-gucci-2.mp4, Negro/Oro=Video-nike-gucci-1.mp4')
    }

    // Create unified product
    const unifiedProduct = {
      id: productId,
      name: parsedDescription.productName || `${cleanProductName(brand)} ${cleanProductName(modelFamily)}`,
      brand: cleanProductName(brand), // Remove number prefix using universal cleaner
      collaboration: cleanProductName(collaboration),
      category: cleanProductName(category).toLowerCase(),
      subcategory: cleanProductName(modelFamily).toLowerCase(),
      gender: cleanProductName(gender).toLowerCase(),
      limitedEdition: true,
      images: mainModel.images,
      videos: mainModel.videos,
      models: allModels, // Use the models array (1 or 2 models depending on product)
      rating: 4.8,
      reviewCount: 127,
      sizes: mainModel.sizes,
      features: ['Materiales Premium', 'Colaboración Exclusiva', 'Edición Limitada'],
      description: parsedDescription.description || `${brand} ${modelFamily} - Colaboración exclusiva`,
      fullDescription: `${parsedDescription.description || `${brand} ${modelFamily}`} - Colaboración exclusiva con materiales premium y diseño único.`,
      materials: ['Cuero Premium', 'Detalles de Lujo', 'Suela Especializada'],
      careInstructions: [
        'Limpiar con paño húmedo',
        'No sumergir en agua',
        'Usar protector de materiales',
        'Almacenar en lugar seco'
      ],
      type: 'sneaker',
      subType: 'lifestyle',
      inStock: true,
      stockCount: 8,
      price: parsedDescription.retailPrice || 2800, // Use calculated retail price (MXN)
      originalPrice: parsedDescription.originalPrice || 3750, // Use calculated original price (MXN)
      sku: parsedDescription.sku || sku,
      modelFamily: cleanProductName(modelFamily),
      modelFamilyDisplay: cleanProductName(modelFamily),
      releaseDate: '2024-03-15'
    }

    console.log('✅ DYNAMIC PRODUCT LOADED:')
    console.log('  - Product Name:', unifiedProduct.name)
    console.log('  - Product Name Type:', typeof unifiedProduct.name)
    console.log('  - Product Name Length:', unifiedProduct.name?.length)
    console.log('  - Product Name First Char:', unifiedProduct.name?.charAt(0))
    console.log('  - Product Name Substring(0,10):', unifiedProduct.name?.substring(0, 10))
    console.log('  - Total Images:', unifiedProduct.images?.length)
    console.log('  - Total Videos:', unifiedProduct.videos?.length)
    console.log('  - Total Models:', unifiedProduct.models?.length)
    console.log('  - Model Names:', unifiedProduct.models?.map(m => m.name).join(', '))

    return unifiedProduct

  } catch (error) {
    console.error('❌ Error loading product from file system:', error)
    return null
  }
}

// Dynamic product path resolver
const resolveProductPath = (productId) => {
  // Parse product ID to extract category, brand, gender, and SKU
  const parts = productId.split('-')

  // Handle specific known products first
  if (productId === 'sneakers-nike-mixte-air-force-bd7700-222') {
    return {
      category: '1. SNEAKERS',
      brand: '1. NIKE Limited Edition',
      modelFamily: '1. AIR FORCE',
      gender: '1. MIXTE',
      collaboration: '1. GUCCI',
      sku: 'BD7700-222',
      productName: 'Gucci'
    }
  }

  if (productId === 'sneakers-nike-mixte-air-force-jgd212-ejd') {
    return {
      category: '1. SNEAKERS',
      brand: '1. NIKE Limited Edition',
      modelFamily: '1. AIR FORCE',
      gender: '1. MIXTE',
      collaboration: '1. GUCCI',
      sku: 'JGD212-EJD',
      productName: 'GUCCI'
    }
  }

  if (productId === 'sneakers-nike-mixte-air-force-jgd212-zzf') {
    return {
      category: '1. SNEAKERS',
      brand: '1. NIKE Limited Edition',
      modelFamily: '1. AIR FORCE',
      gender: '1. MIXTE',
      collaboration: '1. GUCCI',
      sku: 'JGD212-ZZF',
      productName: 'GUCCI'
    }
  }

  if (productId === 'sneakers-nike-mixte-air-force-zed212-edr') {
    return {
      category: '1. SNEAKERS',
      brand: '1. NIKE Limited Edition',
      modelFamily: '1. AIR FORCE',
      gender: '1. MIXTE',
      collaboration: '1. GUCCI',
      sku: 'ZED212-EDR',
      productName: 'GUCCI'
    }
  }

  if (productId === 'sneakers-nike-mixte-air-force-ao4606-001') {
    return {
      category: '1. SNEAKERS',
      brand: '1. NIKE Limited Edition',
      modelFamily: '1. AIR FORCE',
      gender: '1. MIXTE',
      collaboration: '11. OFF WHITE',
      sku: 'AO4606-001',
      productName: 'OW'
    }
  }

  // Handle SNEAKERS category
  if (productId.startsWith('sneakers-')) {
    const [category, brand, gender, model, ...skuParts] = parts
    const sku = skuParts.join('-').toUpperCase()

    // Nike products
    if (brand === 'nike') {
      return {
        category: '1. SNEAKERS',
        brand: '1. NIKE Limited Edition',
        modelFamily: model === 'air-force' ? '1. AIR FORCE' : '2. AIR JORDAN',
        gender: gender === 'mixte' ? '1. MIXTE' : gender === 'women' ? '2. WOMEN' : '3. MEN',
        collaboration: '1. GUCCI', // Default for now
        sku: sku,
        productName: 'GUCCI'
      }
    }

    // Gucci products
    if (brand === 'gucci') {
      return {
        category: '1. SNEAKERS',
        brand: '4. GUCCI',
        modelFamily: '',
        gender: gender === 'mixte' ? '1. MIXTE' : gender === 'women' ? '2. WOMEN' : '3. MEN',
        collaboration: '',
        sku: sku,
        productName: 'Gucci'
      }
    }
  }

  // Handle SANDALS category
  if (productId.startsWith('sandals-')) {
    const [category, brand, gender, ...skuParts] = parts
    const sku = skuParts.join('-').toUpperCase()

    return {
      category: '2. SANDALS',
      brand: brand === 'gucci' ? '2. GUCCI' : brand === 'dior' ? '3. DIOR' : '1. NIKE Collabs',
      modelFamily: '',
      gender: gender === 'mixte' ? '1. MIXTE' : gender === 'women' ? '2. WOMEN' : '3. MEN',
      collaboration: '',
      sku: sku,
      productName: brand.charAt(0).toUpperCase() + brand.slice(1)
    }
  }

  // Handle FORMAL category
  if (productId.startsWith('formal-')) {
    const [category, brand, gender, ...skuParts] = parts
    const sku = skuParts.join('-').toUpperCase()

    return {
      category: '3. FORMAL',
      brand: brand === 'chanel' ? '1. CHANEL' : '3. GUCCI',
      modelFamily: '',
      gender: gender === 'mixte' ? '1. MIXTE' : gender === 'women' ? '2. WOMEN' : '3. MEN',
      collaboration: '',
      sku: sku,
      productName: brand.charAt(0).toUpperCase() + brand.slice(1)
    }
  }

  // Handle CASUAL category
  if (productId.startsWith('casual-')) {
    const [category, brand, gender, ...skuParts] = parts
    const sku = skuParts.join('-').toUpperCase()

    return {
      category: '4. CASUAL',
      brand: brand === 'ugg' ? '1. UGG' : brand === 'lv' ? '2. LV' : '6. GUCCI',
      modelFamily: '',
      gender: gender === 'mixte' ? '1. MIXTE' : gender === 'women' ? '2. WOMEN' : '3. MEN',
      collaboration: '',
      sku: sku,
      productName: brand.charAt(0).toUpperCase() + brand.slice(1)
    }
  }

  // Handle KIDS category
  if (productId.startsWith('kids-')) {
    const [category, brand, gender, ...skuParts] = parts
    const sku = skuParts.join('-').toUpperCase()

    return {
      category: '5. KIDS',
      brand: brand === 'ugg' ? '1. UGG' : '2. GOLDEN GOOSE',
      modelFamily: '',
      gender: '1. MIXTE', // Kids are usually unisex
      collaboration: '',
      sku: sku,
      productName: brand.charAt(0).toUpperCase() + brand.slice(1)
    }
  }

  // Add more product path mappings here as needed
  return null
}

// Load real product data directly from file system (not database)
export const loadRealProduct = async (productId) => {
  console.log('🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀 REAL PRODUCT LOADER CALLED!')
  console.log('🚀🚀🚀 REAL PRODUCT LOADER CALLED WITH ID:', productId)
  console.log('🚀🚀🚀 CHECKING IF BD7700-222:', productId === 'sneakers-nike-mixte-air-force-bd7700-222')
  console.log('🚀🚀🚀 FUNCTION IS EXECUTING - THIS SHOULD ALWAYS SHOW!')

  // 🏢 ENTERPRISE SYSTEM INTEGRATION (Future Enhancement)
  // Note: Enterprise catalog system will be integrated in future versions

  try {
    console.log('🔍🔍🔍 INSIDE TRY BLOCK')

    // Resolve product path dynamically
    const pathInfo = resolveProductPath(productId)
    console.log('🔍🔍🔍 Resolved path info:', pathInfo)
    console.log('🔍🔍🔍 PathInfo exists?', !!pathInfo)

    if (pathInfo) {
      console.log('🔍🔍🔍 TAKING DYNAMIC PATH - CALLING loadProductFromFileSystem')
      return await loadProductFromFileSystem(pathInfo, productId)
    }
    console.log('🔍🔍🔍 NO DYNAMIC PATH - CONTINUING TO BD7700-222 SPECIFIC HANDLING')

    // For BD7700-222, load directly from file system (legacy support)
    if (productId === 'sneakers-nike-mixte-air-force-bd7700-222') {
      console.log('🔥🔥🔥 ENTERING BD7700-222 SPECIAL HANDLING!')
      const productPath = '/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- Gucci'

      // Load images and videos from the actual folder
      const images = [
        `${productPath}/o_1hfi0lgi514331ru41hu4km31qsp47.webp`,
        `${productPath}/o_1hfi0lgi61ad617f41o9k1peh1uq548.webp`,
        `${productPath}/o_1hfi0lgi6apo15rbmvq2eco3f49.webp`,
        `${productPath}/o_1hfi0lgi71ic1jnt1b09fo61cjn4a.webp`,
        `${productPath}/o_1hfi0lgi81mmvp4e1ru65dbqk4c.webp`,
        `${productPath}/o_1hfi0lgi8lta26dngkj9ns084b.webp`,
        `${productPath}/o_1hfi0lgi91qrd1s7u19bpbfp1lqc4e.webp`,
        `${productPath}/o_1hfi0lgi91uti1iq78tphq7a3b4f.webp`,
        `${productPath}/o_1hfi0lgi962u1l1nnj11m0r167o4d.webp`,
        `${productPath}/o_1hjg0hb8f1car11sv1vm3sj51o1fi.webp`,
        `${productPath}/o_1hjg0hb8gg271iu61n8v1rus855j.webp`,
        `${productPath}/o_1hjg0hb8gjptplevau157o1jb6k.webp`,
        `${productPath}/o_1hjg0hb8h13pr1b171o17lfi1ec4l.webp`,
        `${productPath}/o_1hjg0hb8hjcrvjtlji1cgn1qjum.webp`,
        `${productPath}/o_1hjg0hb8i12p42vvh0i1n878p3n.webp`,
        `${productPath}/o_1hjg0hb8i1ebk154d7bd4m016u3o.webp`,
        `${productPath}/o_1hjg0hb8itnt1380trf1p0e5nfp.webp`,
        `${productPath}/o_1hjg0hb8jspr9c21dmjm2r1a3vq.webp`
      ]

      const videos = [
        `${productPath}/Video-nike-gucci-1.mp4`,
        `${productPath}/Video-nike-gucci-2.mp4`
      ]

      // Load description from .txt file
      const descriptionText = await loadProductDescription(productPath)
      const parsedDescription = parseProductDescription(descriptionText, 'NIKE Limited Edition', 'GUCCI', 'AIR FORCE')

      console.log('📄 Description loaded:', parsedDescription)

      // Create the main model with videos FIRST, then images
      const mainModel = {
        id: 'bd7700-222-main',
        name: parsedDescription.productName || 'BD7700-222 Gucci',
        sku: parsedDescription.sku || 'BD7700-222',
        price: parsedDescription.retailPrice || 2800, // Use calculated retail price (MXN)
        originalPrice: parsedDescription.originalPrice || 3750, // Use calculated original price (MXN)
        images: images,
        videos: videos,
        inStock: true,
        stock: 5,
        colors: ['Blanco/Verde'],
        description: parsedDescription.description || 'Nike Air Force 1 x Gucci - Edición Limitada',
        sizes: parsedDescription.sizes || ['36', '36.5', '37.5', '38', '38.5', '39', '40', '40.5', '41', '42', '42.5', '43', '44', '44.5', '45']
      }

      // Create a second model (variant) from the same BD7700-222 folder
      // Split the 18 images into 2 sets of 9 images each (2 colorways)
      const allImages = [
        `${productPath}/o_1hfi0lgi514331ru41hu4km31qsp47.webp`,
        `${productPath}/o_1hfi0lgi61ad617f41o9k1peh1uq548.webp`,
        `${productPath}/o_1hfi0lgi6apo15rbmvq2eco3f49.webp`,
        `${productPath}/o_1hfi0lgi71ic1jnt1b09fo61cjn4a.webp`,
        `${productPath}/o_1hfi0lgi81mmvp4e1ru65dbqk4c.webp`,
        `${productPath}/o_1hfi0lgi8lta26dngkj9ns084b.webp`,
        `${productPath}/o_1hfi0lgi91qrd1s7u19bpbfp1lqc4e.webp`,
        `${productPath}/o_1hfi0lgi91uti1iq78tphq7a3b4f.webp`,
        `${productPath}/o_1hfi0lgi962u1l1nnj11m0r167o4d.webp`,
        `${productPath}/o_1hjg0hb8f1car11sv1vm3sj51o1fi.webp`,
        `${productPath}/o_1hjg0hb8gg271iu61n8v1rus855j.webp`,
        `${productPath}/o_1hjg0hb8gjptplevau157o1jb6k.webp`,
        `${productPath}/o_1hjg0hb8h13pr1b171o17lfi1ec4l.webp`,
        `${productPath}/o_1hjg0hb8hjcrvjtlji1cgn1qjum.webp`,
        `${productPath}/o_1hjg0hb8i12p42vvh0i1n878p3n.webp`,
        `${productPath}/o_1hjg0hb8i1ebk154d7bd4m016u3o.webp`,
        `${productPath}/o_1hjg0hb8itnt1380trf1p0e5nfp.webp`,
        `${productPath}/o_1hjg0hb8jspr9c21dmjm2r1a3vq.webp`
      ]

      // First colorway (first 9 images) - already used in mainModel
      const firstColorwayImages = allImages.slice(0, 9)

      // Second colorway (last 9 images) - for variant model
      const secondColorwayImages = allImages.slice(9, 18)

      // Update main model to use only first colorway images
      mainModel.images = firstColorwayImages

      // Use the same videos for the variant model (since they're the same product)
      const variantVideos = [
        `${productPath}/Video-nike-gucci-1.mp4`,
        `${productPath}/Video-nike-gucci-2.mp4`
      ]

      console.log('📄 Creating second colorway variant from same product folder')

      const variantModel = {
        id: 'bd7700-222-variant',
        name: parsedDescription.productName || 'BD7700-222 Gucci (Colorway 2)',
        sku: 'BD7700-222-V2',
        price: parsedDescription.retailPrice || 3570, // Same price as main model (MXN)
        originalPrice: parsedDescription.originalPrice || 4760, // Same price as main model (MXN)
        images: secondColorwayImages,
        videos: variantVideos,
        inStock: true,
        stock: 3,
        colors: ['Negro/Oro'],
        description: parsedDescription.description || 'Nike Air Force 1 x Gucci - Colorway 2',
        sizes: parsedDescription.sizes || ['36', '36.5', '37.5', '38', '38.5', '39', '40', '40.5', '41', '42', '42.5', '43', '44', '44.5', '45']
      }

      const unifiedProduct = {
        id: 'sneakers-nike-mixte-air-force-bd7700-222',
        name: parsedDescription.productName || 'Nike Air Force 1 x Gucci',
        brand: 'Nike',
        collaboration: 'Gucci x Nike',
        category: 'sneakers',
        subcategory: 'air-force',
        gender: 'mixte',
        limitedEdition: true,
        cytteFolder: '/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- Gucci',
        categoryInfo: {
          primary: 'sneakers',
          secondary: 'air-force',
          cytteCategory: '1. SNEAKERS',
          cytte: true
        },
        images: mainModel.images, // Use main model images as primary
        videos: mainModel.videos, // Include videos
        models: [mainModel, variantModel], // Only 2 models
        rating: 4.8,
        reviewCount: 127,
        sizes: mainModel.sizes, // Use sizes from main model description
        features: ['Materiales Premium', 'Colaboración Exclusiva', 'Edición Limitada'],
        description: parsedDescription.description || 'Nike Air Force 1 x Gucci - Colaboración exclusiva',
        fullDescription: `${parsedDescription.description || 'Nike Air Force 1 x Gucci'} - La colaboración más exclusiva entre Nike y Gucci. Air Force 1 con detalles de lujo y materiales premium. Horma original y cartón original para crear una versión puramente baja de la fuerza aérea. Colchón de aire en forma de panal incorporado en toda su longitud con accesorios originales de la caja.`,
        materials: ['Cuero Premium', 'Detalles Gucci', 'Suela de Goma Especializada', 'Colchón de Aire'],
        careInstructions: [
          'Limpiar con paño húmedo',
          'No sumergir en agua',
          'Usar protector de cuero',
          'Almacenar en lugar seco',
          'Evitar exposición directa al sol'
        ],
        type: 'sneaker',
        subType: 'lifestyle',
        inStock: true,
        stockCount: 8,
        price: parsedDescription.retailPrice || 2800, // Use calculated retail price (MXN)
        originalPrice: parsedDescription.originalPrice || 3750, // Use calculated original price (MXN)
        sku: parsedDescription.sku || 'BD7700-222',
        modelFamily: 'Air Force',
        modelFamilyDisplay: 'Air Force 1',
        releaseDate: '2024-03-15'
      }

      console.log('✅ REAL PRODUCT LOADED:')
      console.log('  - Product Name:', unifiedProduct.name)
      console.log('  - Total Images:', unifiedProduct.images?.length)
      console.log('  - Total Videos:', unifiedProduct.videos?.length)
      console.log('  - Total Models:', unifiedProduct.models?.length)
      console.log('  - Model Names:', unifiedProduct.models?.map(m => m.name).join(', '))

      return unifiedProduct
    }

    // Fallback to database for other products
    const targetProduct = cytteProducts.find(p =>
      p.id === productId ||
      p.sku === productId ||
      p.id.toString() === productId
    )

    if (!targetProduct) {
      console.log('❌ Product not found:', productId)
      return null
    }

    return targetProduct

  } catch (error) {
    console.error('❌ Error loading real product:', error)
    console.error('❌ Error details:', error.message)
    console.error('❌ Error stack:', error.stack)
    return null
  }
}

// Get all real products with corrected paths
export const getAllRealProducts = () => {
  try {
    console.log('🔍🔍🔍 getAllRealProducts called, products.length:', products.length)
    console.log('🔍🔍🔍 products type:', typeof products)
    console.log('🔍🔍🔍 products is array:', Array.isArray(products))

    if (products.length > 0) {
      console.log('🔍🔍🔍 First product sample:', {
        id: products[0].id,
        name: products[0].name,
        originalImages: products[0].images?.slice(0, 2),
      })
    }

    const result = products.map(product => ({
      ...product,
      images: product.images?.map(img => {
        const converted = convertDatabasePathToActualPath(img)
        if (img !== converted) {
          console.log('🔍🔍🔍 Path conversion:', img, '→', converted)
        }
        return converted
      }) || []
    }))

    if (result.length > 0) {
      console.log('🔍🔍🔍 First result sample:', {
        id: result[0].id,
        name: result[0].name,
        convertedImages: result[0].images?.slice(0, 2),
      })
    }

    console.log('🔍🔍🔍 getAllRealProducts returning:', result.length, 'products')
    return result
  } catch (error) {
    console.error('🔍🔍🔍 ERROR in getAllRealProducts:', error)
    throw error
  }
}

// Enhanced category mapping for CYTTE structure
const mapCytteToCategory = (cytteFolder) => {
  if (cytteFolder.includes('1. SNEAKERS')) return 'sneakers'
  if (cytteFolder.includes('2. SANDALS')) return 'sandals'
  if (cytteFolder.includes('3. FORMAL')) return 'formal'
  if (cytteFolder.includes('4. CASUAL')) return 'casual'
  if (cytteFolder.includes('5. KIDS')) return 'kids'
  return 'sneakers' // Default fallback
}

// Get products by category
export const getRealProductsByCategory = (category) => {
  const allProducts = getAllRealProducts()
  return allProducts.filter(product => {
    // Primary matching: direct category match
    if (product.category === category || product.style === category || product.type === category) {
      return true
    }

    // Secondary matching: map from CYTTE folder structure
    if (product.cytteFolder) {
      const mappedCategory = mapCytteToCategory(product.cytteFolder)
      return mappedCategory === category
    }

    // Tertiary matching: by product ID prefix
    if (product.id && product.id.startsWith(`${category}-`)) {
      return true
    }

    return false
  })
}

// Get products by brand
export const getRealProductsByBrand = (brand) => {
  const allProducts = getAllRealProducts()
  return allProducts.filter(product => 
    product.brand?.toLowerCase().includes(brand.toLowerCase()) ||
    product.brandId?.toLowerCase().includes(brand.toLowerCase())
  )
}

// Search products
export const searchRealProducts = (query) => {
  const allProducts = getAllRealProducts()
  const searchTerm = query.toLowerCase()
  
  return allProducts.filter(product => 
    product.name?.toLowerCase().includes(searchTerm) ||
    product.brand?.toLowerCase().includes(searchTerm) ||
    product.description?.toLowerCase().includes(searchTerm) ||
    product.keywords?.some(keyword => keyword.toLowerCase().includes(searchTerm)) ||
    product.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
  )
}

// Get featured products
export const getFeaturedRealProducts = () => {
  const allProducts = getAllRealProducts()
  return allProducts.filter(product => product.isFeatured || product.featured)
}

// Get new products
export const getNewRealProducts = () => {
  const allProducts = getAllRealProducts()
  return allProducts.filter(product => product.isNew)
}

// Get limited edition products
export const getLimitedRealProducts = () => {
  const allProducts = getAllRealProducts()
  return allProducts.filter(product => product.isLimited)
}

// Export utility functions for use in components
export { cleanProductName }
