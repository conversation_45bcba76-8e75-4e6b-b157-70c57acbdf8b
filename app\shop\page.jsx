'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { products as mockProducts, brands, categories, filterProducts, sortProducts } from '@/data/products'
import { getAllRealProducts } from '@/lib/real-products-loader'
import AnimatedProductCard from '@/components/ui/AnimatedProductCard'
import MobileProductCard from '@/components/ui/MobileProductCard'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import EnhancedSearchBar from '@/components/features/EnhancedSearchBar'
import Badge from '@/components/ui/Badge'

import AuthModal from '@/components/ui/AuthModal'
import EnhancedVoiceSearch from '@/components/features/EnhancedVoiceSearch'
import VisualSearch from '@/components/features/VisualSearch'
import { MobileContainer, MobileSection, MobileGrid, MobileCard, MobileButton, MobileBottomSheet } from '@/components/mobile/MobileContainer'

export default function ShopPage() {
  console.log('🛍️🛍️🛍️ SHOP PAGE COMPONENT LOADING!')
  console.log('🛍️🛍️🛍️ SHOP PAGE: getAllRealProducts function type:', typeof getAllRealProducts)
  console.log('🛍️🛍️🛍️ SHOP PAGE: mockProducts length:', mockProducts.length)

  const searchParams = useSearchParams()
  const [products, setProducts] = useState([])
  const [isLoadingProducts, setIsLoadingProducts] = useState(true)
  const [productsError, setProductsError] = useState(null)
  const [retryCount, setRetryCount] = useState(0)
  const [gridPerformance, setGridPerformance] = useState({
    renderTime: 0,
    itemCount: 0,
    breakpoint: 'unknown'
  })
  const [filteredProducts, setFilteredProducts] = useState(mockProducts)
  const [filters, setFilters] = useState({
    search: '',
    brand: '',
    category: '',
    gender: '',
    priceRange: { min: 0, max: 50000 },
    size: '',
    isLimited: false,
    isNew: false
  })
  const [sortBy, setSortBy] = useState('newest')
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [productsPerPage] = useState(12)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [isVoiceSearchOpen, setIsVoiceSearchOpen] = useState(false)
  const [isVisualSearchOpen, setIsVisualSearchOpen] = useState(false)
  const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Handle URL parameters (brand filtering from brand showcase)
  useEffect(() => {
    const brandParam = searchParams.get('brand')
    if (brandParam) {
      console.log(`🔗 Brand filter from URL: ${brandParam}`)
      // Map brand slug back to brand name for filtering
      const brandName = brandParam.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      console.log(`🔗 Mapped brand name: ${brandName}`)

      setFilters(prev => ({
        ...prev,
        brand: brandParam
      }))

      // Show filters sidebar when coming from brand showcase
      setShowFilters(true)
    }
  }, [searchParams])

  // Load real products on mount with comprehensive error handling
  useEffect(() => {
    const loadRealProducts = async () => {
      setIsLoadingProducts(true)
      setProductsError(null)

      try {
        console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: About to call getAllRealProducts() - Attempt:', retryCount + 1)
        console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: mockProducts count:', mockProducts.length)

        // Always try to get real products first
        const realProducts = getAllRealProducts()
        console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: getAllRealProducts() returned:', realProducts.length, 'products')

        if (realProducts.length > 0) {
          console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: ✅ Using REAL products with /products/ paths')
          console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: First real product sample:', {
            id: realProducts[0].id,
            name: realProducts[0].name,
            images: realProducts[0].images?.slice(0, 2)
          })
          setProducts(realProducts)
          setProductsError(null)
        } else {
          console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: ⚠️ No real products found, converting mock products to use /products/ paths')

          // Convert mock products to use /products/ paths instead of /products-organized/
          const convertedMockProducts = mockProducts.map(product => ({
            ...product,
            images: product.images?.map(img =>
              img.replace('/products-organized/', '/products/')
            ) || [],
            videos: product.videos?.map(video =>
              video.replace('/products-organized/', '/products/')
            ) || []
          }))

          console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: ✅ Converted mock products to use /products/ paths')
          console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: First converted product sample:', {
            id: convertedMockProducts[0]?.id,
            name: convertedMockProducts[0]?.name,
            images: convertedMockProducts[0]?.images?.slice(0, 2)
          })
          setProducts(convertedMockProducts)
          setProductsError(null)
        }
      } catch (error) {
        console.error('🛍️🛍️🛍️ SHOP PAGE CLIENT: Error loading real products:', error)
        console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: ⚠️ Error occurred, converting mock products as fallback')

        setProductsError({
          message: 'Error al cargar productos',
          details: error.message,
          canRetry: true
        })

        // Even in error case, convert mock products to use correct paths
        const convertedMockProducts = mockProducts.map(product => ({
          ...product,
          images: product.images?.map(img =>
            img.replace('/products-organized/', '/products/')
          ) || [],
          videos: product.videos?.map(video =>
            video.replace('/products-organized/', '/products/')
          ) || []
        }))

        setProducts(convertedMockProducts)
      } finally {
        setIsLoadingProducts(false)
      }
    }

    loadRealProducts()
  }, [retryCount])

  // Retry function for error handling
  const retryLoadProducts = () => {
    setRetryCount(prev => prev + 1)
  }



  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Apply filters and sorting with enhanced data source integration
  useEffect(() => {
    // Always prioritize the products state (which contains either real products or converted mock products)
    const sourceProducts = products.length > 0 ? products : []
    console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: Enhanced sourceProducts selection:', {
      productsStateCount: products.length,
      mockProductsCount: mockProducts.length,
      usingProductsState: products.length > 0,
      sourceProductsCount: sourceProducts.length,
      dataSource: products.length > 0 ? 'products-state' : 'empty'
    })

    if (sourceProducts.length > 0) {
      console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: ✅ Using products state with correct /products/ paths')
      console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: First source product sample:', {
        id: sourceProducts[0].id,
        name: sourceProducts[0].name,
        images: sourceProducts[0].images?.slice(0, 2)
      })
    } else {
      console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: ⚠️ No products in state, waiting for data to load...')
    }

    let filtered = [...sourceProducts]

    // Apply brand filter
    if (filters.brand) {
      const brandSlug = filters.brand.toLowerCase()
      filtered = filtered.filter(product => {
        const productBrandSlug = product.brand.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
        return productBrandSlug === brandSlug
      })
      console.log(`🔍 Brand filter applied: ${filters.brand}, found ${filtered.length} products`)
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(product =>
        product.category?.toLowerCase() === filters.category.toLowerCase()
      )
    }

    // Apply search
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.brand.toLowerCase().includes(searchTerm) ||
        (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
      )
    }

    // Apply other filters
    if (filters.gender) {
      filtered = filtered.filter(product =>
        product.gender?.toLowerCase() === filters.gender.toLowerCase()
      )
    }

    if (filters.isLimited) {
      filtered = filtered.filter(product => product.limitedEdition === true)
    }

    if (filters.isNew) {
      filtered = filtered.filter(product => product.isNew === true)
    }

    // Apply price range filter
    if (filters.priceRange.min > 0 || filters.priceRange.max < 50000) {
      filtered = filtered.filter(product =>
        product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
      )
    }

    // Sort products
    filtered = sortProducts(filtered, sortBy)

    setFilteredProducts(filtered)
    setCurrentPage(1) // Reset to first page when filters change
  }, [filters, sortBy, products])

  // Pagination
  const indexOfLastProduct = currentPage * productsPerPage
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct)
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage)

  // Grid Performance Monitoring
  useEffect(() => {
    const startTime = performance.now()

    // Detect current breakpoint
    const detectBreakpoint = () => {
      if (typeof window === 'undefined') return 'SSR'
      const width = window.innerWidth
      if (width >= 1536) return '2XL'
      if (width >= 1280) return 'XL'
      if (width >= 1024) return 'LG'
      if (width >= 768) return 'MD'
      if (width >= 640) return 'SM'
      return 'XS'
    }

    const updateGridPerformance = () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime

      setGridPerformance({
        renderTime: Math.round(renderTime * 100) / 100,
        itemCount: currentProducts.length,
        breakpoint: detectBreakpoint()
      })
    }

    // Update performance after products are rendered
    if (currentProducts.length > 0) {
      requestAnimationFrame(updateGridPerformance)
    }

    // Listen for resize events to update breakpoint
    const handleResize = () => {
      setGridPerformance(prev => ({
        ...prev,
        breakpoint: detectBreakpoint()
      }))
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [currentProducts])

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      brand: '',
      category: '',
      gender: '',
      priceRange: { min: 0, max: 50000 },
      size: '',
      isLimited: false,
      isNew: false
    })
  }

  const activeFiltersCount = Object.values(filters).filter(value => {
    if (typeof value === 'string') return value !== ''
    if (typeof value === 'boolean') return value
    if (typeof value === 'object' && value.min !== undefined) {
      return value.min !== 0 || value.max !== 50000
    }
    return false
  }).length

  const handleVoiceSearchResults = (results, query) => {
    handleFilterChange('search', query)
    setIsVoiceSearchOpen(false)
  }

  const handleVisualSearch = (searchTerm) => {
    // For visual search, we'll use the search term provided
    handleFilterChange('search', searchTerm)
    setIsVisualSearchOpen(false)
  }

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine">
        <MobileContainer>
          {/* Mobile Header */}
          <MobileSection
            title="Tienda"
            subtitle="Sneakers de lujo exclusivos"
            className="pt-4"
          >
            {/* Mobile Search */}
            <div className="mb-4">
              <EnhancedSearchBar
                placeholder="Buscar productos... 🎤 📸"
                onSearch={(searchTerm) => handleFilterChange('search', searchTerm)}
              />
            </div>

            {/* Mobile Filter & Sort Bar */}
            <div className="flex gap-2 mb-6">
              <MobileButton
                variant="secondary"
                onClick={() => setIsMobileFiltersOpen(true)}
                className="flex-1"
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                  </svg>
                }
              >
                Filtros {activeFiltersCount > 0 && `(${activeFiltersCount})`}
              </MobileButton>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 glass rounded-xl text-forest-emerald dark:text-light-cloud-gray text-sm focus:outline-none focus:ring-2 focus:ring-rich-gold"
              >
                <option value="newest">Recientes</option>
                <option value="price-low">Precio ↑</option>
                <option value="price-high">Precio ↓</option>
                <option value="rating">Mejor valorados</option>
              </select>
            </div>
          </MobileSection>

          {/* Mobile Products Grid */}
          <MobileContainer noPadding>
            <div className="px-4">
              <p className="text-warm-camel text-sm mb-4">
                {filteredProducts.length} productos encontrados
              </p>
            </div>

            <div className="px-4">
              <div className="product-grid-mobile">
                <AnimatePresence mode="popLayout">
                  {currentProducts.map((product, index) => (
                    <div key={product.id} className="product-card-mobile">
                      <MobileProductCard
                        product={product}
                        index={index}
                      />
                    </div>
                  ))}
                </AnimatePresence>
              </div>
            </div>

            {/* Mobile Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2 p-4 mt-6">
                <MobileButton
                  variant="secondary"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                  size="sm"
                >
                  ←
                </MobileButton>

                <span className="flex items-center px-3 text-sm text-warm-camel">
                  {currentPage} de {totalPages}
                </span>

                <MobileButton
                  variant="secondary"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  size="sm"
                >
                  →
                </MobileButton>
              </div>
            )}

            {/* No Results - Mobile */}
            {filteredProducts.length === 0 && (
              <div className="text-center py-12 px-4">
                <div className="text-6xl mb-4">👟</div>
                <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-warm-camel mb-4 text-sm">
                  Intenta ajustar tus filtros o buscar algo diferente
                </p>
                <MobileButton variant="primary" onClick={clearFilters}>
                  Limpiar Filtros
                </MobileButton>
              </div>
            )}
          </MobileContainer>
        </MobileContainer>

        {/* Mobile Filters Bottom Sheet */}
        <MobileBottomSheet
          isOpen={isMobileFiltersOpen}
          onClose={() => setIsMobileFiltersOpen(false)}
          title="Filtros"
        >
          <div className="p-6 space-y-6">
            {/* Categories */}
            <div>
              <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                Categorías
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {categories.map(category => (
                  <MobileButton
                    key={category.id}
                    variant={filters.category === category.id ? 'primary' : 'secondary'}
                    onClick={() => handleFilterChange('category', category.id)}
                    size="sm"
                    className="justify-start"
                  >
                    {category.icon} {category.name}
                  </MobileButton>
                ))}
              </div>
            </div>

            {/* Brands */}
            <div>
              <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                Marcas
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {brands.map(brand => (
                  <MobileButton
                    key={brand.id}
                    variant={filters.brand === brand.id ? 'primary' : 'secondary'}
                    onClick={() => handleFilterChange('brand', brand.id)}
                    size="sm"
                  >
                    {brand.name}
                  </MobileButton>
                ))}
              </div>
            </div>

            {/* Special Filters */}
            <div>
              <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                Especiales
              </h4>
              <div className="space-y-2">
                <MobileButton
                  variant={filters.isLimited ? 'primary' : 'secondary'}
                  onClick={() => handleFilterChange('isLimited', !filters.isLimited)}
                  fullWidth
                  size="sm"
                >
                  🔥 Edición Limitada
                </MobileButton>
                <MobileButton
                  variant={filters.isNew ? 'primary' : 'secondary'}
                  onClick={() => handleFilterChange('isNew', !filters.isNew)}
                  fullWidth
                  size="sm"
                >
                  ✨ Nuevos Lanzamientos
                </MobileButton>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-4 border-t border-white/10">
              <MobileButton
                variant="secondary"
                onClick={clearFilters}
                fullWidth
              >
                Limpiar
              </MobileButton>
              <MobileButton
                variant="primary"
                onClick={() => setIsMobileFiltersOpen(false)}
                fullWidth
              >
                Aplicar
              </MobileButton>
            </div>
          </div>
        </MobileBottomSheet>

        {/* AI Search Modals */}
        <EnhancedVoiceSearch
          isOpen={isVoiceSearchOpen}
          onClose={() => setIsVoiceSearchOpen(false)}
          onResults={handleVoiceSearchResults}
        />

        <VisualSearch
          isOpen={isVisualSearchOpen}
          onClose={() => setIsVisualSearchOpen(false)}
          onSearch={handleVisualSearch}
        />
      </div>
    )
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-godber font-bold text-pure-black dark:text-pure-white mb-4">
            {filters.brand ?
              `Catálogo ${filters.brand.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}` :
              'Tienda'
            }
          </h1>
          <p className="text-text-gray text-lg font-poppins">
            {filters.brand ?
              `Todos los productos de ${filters.brand.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}` :
              'Descubre nuestra colección exclusiva de sneakers de lujo'
            }
          </p>
        </motion.div>

        {/* Search and Sort Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col sm:flex-row gap-4 mb-6"
        >
          {/* Enhanced Search */}
          <div className="flex-1">
            <EnhancedSearchBar
              placeholder="Buscar productos, marcas... 🎤 📸"
              onSearch={(searchTerm) => handleFilterChange('search', searchTerm)}
            />
          </div>

          {/* Sort */}
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input-glass rounded-lg px-4 py-3 text-forest-emerald dark:text-light-cloud-gray focus:outline-none focus:ring-2 focus:ring-rich-gold"
            >
              <option value="newest">Más Recientes</option>
              <option value="price-low">Precio: Menor a Mayor</option>
              <option value="price-high">Precio: Mayor a Menor</option>
              <option value="name">Nombre A-Z</option>
              <option value="brand">Marca A-Z</option>
              <option value="rating">Mejor Valorados</option>
            </select>

            <Button
              variant={showFilters ? 'primary' : 'secondary'}
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              Filtros
              {activeFiltersCount > 0 && (
                <Badge
                  variant="primary"
                  size="sm"
                  className="absolute -top-2 -right-2 min-w-[20px] h-5 flex items-center justify-center text-xs"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </div>
        </motion.div>

        <div className="flex gap-8">
          {/* Enterprise-Grade Luxury Filter Panel */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, x: -300, scale: 0.95 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: -300, scale: 0.95 }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                  mass: 0.8
                }}
                className="w-80 space-y-6"
              >
                {/* Glassmorphic Filter Panel */}
                <div className="backdrop-blur-xl bg-white/10 dark:bg-black/20 border border-white/20 dark:border-white/10 rounded-2xl shadow-2xl overflow-hidden">
                  {/* Header with Luxury Gradient */}
                  <div className="relative p-6 bg-gradient-to-r from-lime-400/20 via-lime-500/10 to-lime-600/20 border-b border-white/10">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
                    <div className="relative flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-lg bg-lime-400/20 flex items-center justify-center">
                          <svg className="w-4 h-4 text-lime-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-godber font-bold text-pure-black dark:text-pure-white">
                          Filtros
                        </h3>
                      </div>
                      {activeFiltersCount > 0 && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={clearFilters}
                          className="px-3 py-1.5 text-xs font-medium bg-red-500/20 text-red-400 rounded-lg border border-red-500/30 hover:bg-red-500/30 transition-all duration-200"
                        >
                          Limpiar ({activeFiltersCount})
                        </motion.button>
                      )}
                    </div>
                  </div>

                  {/* Filter Content with Luxury Styling */}
                  <div className="p-6 space-y-8">
                    {/* Categories Section */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-1 h-4 bg-gradient-to-b from-lime-400 to-lime-600 rounded-full"></div>
                        <h4 className="font-godber font-semibold text-pure-black dark:text-pure-white">
                          Categorías
                        </h4>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        {categories.map(category => (
                          <motion.label
                            key={category.id}
                            className={`relative cursor-pointer group`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <input
                              type="radio"
                              name="category"
                              value={category.id}
                              checked={filters.category === category.id}
                              onChange={(e) => handleFilterChange('category', e.target.value)}
                              className="sr-only"
                            />
                            <div className={`
                              p-3 rounded-xl border transition-all duration-200
                              ${filters.category === category.id
                                ? 'bg-lime-400/20 border-lime-400/50 shadow-lg shadow-lime-400/20'
                                : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
                              }
                            `}>
                              <div className="flex items-center gap-2">
                                <span className="text-lg">{category.icon}</span>
                                <span className={`text-xs font-medium ${
                                  filters.category === category.id
                                    ? 'text-lime-400'
                                    : 'text-warm-camel group-hover:text-pure-white dark:group-hover:text-pure-white'
                                }`}>
                                  {category.name}
                                </span>
                              </div>
                            </div>
                          </motion.label>
                        ))}
                      </div>
                    </div>

                    {/* Brands Section */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-1 h-4 bg-gradient-to-b from-lime-400 to-lime-600 rounded-full"></div>
                        <h4 className="font-godber font-semibold text-pure-black dark:text-pure-white">
                          Marcas de Lujo
                        </h4>
                      </div>
                      <div className="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                        {brands.map(brand => (
                          <motion.label
                            key={brand.id}
                            className="flex items-center gap-3 cursor-pointer group p-2 rounded-lg hover:bg-white/5 transition-all duration-200"
                            whileHover={{ x: 4 }}
                          >
                            <div className="relative">
                              <input
                                type="radio"
                                name="brand"
                                value={brand.id}
                                checked={filters.brand === brand.id}
                                onChange={(e) => handleFilterChange('brand', e.target.value)}
                                className="sr-only"
                              />
                              <div className={`
                                w-4 h-4 rounded-full border-2 transition-all duration-200
                                ${filters.brand === brand.id
                                  ? 'border-lime-400 bg-lime-400'
                                  : 'border-white/30 group-hover:border-lime-400/50'
                                }
                              `}>
                                {filters.brand === brand.id && (
                                  <div className="w-2 h-2 bg-pure-black rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                                )}
                              </div>
                            </div>
                            <span className={`text-sm font-medium transition-colors duration-200 ${
                              filters.brand === brand.id
                                ? 'text-lime-400'
                                : 'text-warm-camel group-hover:text-pure-white dark:group-hover:text-pure-white'
                            }`}>
                              {brand.name}
                            </span>
                          </motion.label>
                        ))}
                      </div>
                    </div>

                    {/* Gender Section */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-1 h-4 bg-gradient-to-b from-lime-400 to-lime-600 rounded-full"></div>
                        <h4 className="font-godber font-semibold text-pure-black dark:text-pure-white">
                          Género
                        </h4>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        {[
                          { id: 'men', label: 'Hombre', icon: '👨' },
                          { id: 'women', label: 'Mujer', icon: '👩' },
                          { id: 'unisex', label: 'Unisex', icon: '👤' }
                        ].map(gender => (
                          <motion.label
                            key={gender.id}
                            className="cursor-pointer group"
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <input
                              type="radio"
                              name="gender"
                              value={gender.id}
                              checked={filters.gender === gender.id}
                              onChange={(e) => handleFilterChange('gender', e.target.value)}
                              className="sr-only"
                            />
                            <div className={`
                              p-3 rounded-xl border transition-all duration-200 text-center
                              ${filters.gender === gender.id
                                ? 'bg-lime-400/20 border-lime-400/50 shadow-lg shadow-lime-400/20'
                                : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
                              }
                            `}>
                              <div className="text-lg mb-1">{gender.icon}</div>
                              <span className={`text-xs font-medium ${
                                filters.gender === gender.id
                                  ? 'text-lime-400'
                                  : 'text-warm-camel group-hover:text-pure-white dark:group-hover:text-pure-white'
                              }`}>
                                {gender.label}
                              </span>
                            </div>
                          </motion.label>
                        ))}
                      </div>
                    </div>

                    {/* Special Filters Section */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-1 h-4 bg-gradient-to-b from-orange-400 to-red-500 rounded-full"></div>
                        <h4 className="font-godber font-semibold text-pure-black dark:text-pure-white">
                          Ediciones Especiales
                        </h4>
                      </div>
                      <div className="space-y-3">
                        <motion.label
                          className="flex items-center gap-3 cursor-pointer group p-3 rounded-xl bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 hover:border-orange-500/40 transition-all duration-200"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="relative">
                            <input
                              type="checkbox"
                              checked={filters.isLimited}
                              onChange={(e) => handleFilterChange('isLimited', e.target.checked)}
                              className="sr-only"
                            />
                            <div className={`
                              w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center
                              ${filters.isLimited
                                ? 'border-orange-400 bg-orange-400'
                                : 'border-white/30 group-hover:border-orange-400/50'
                              }
                            `}>
                              {filters.isLimited && (
                                <svg className="w-3 h-3 text-pure-black" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-lg">🔥</span>
                            <span className={`text-sm font-medium ${
                              filters.isLimited ? 'text-orange-400' : 'text-warm-camel group-hover:text-orange-400'
                            }`}>
                              Edición Limitada
                            </span>
                          </div>
                        </motion.label>

                        <motion.label
                          className="flex items-center gap-3 cursor-pointer group p-3 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-200"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="relative">
                            <input
                              type="checkbox"
                              checked={filters.isNew}
                              onChange={(e) => handleFilterChange('isNew', e.target.checked)}
                              className="sr-only"
                            />
                            <div className={`
                              w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center
                              ${filters.isNew
                                ? 'border-blue-400 bg-blue-400'
                                : 'border-white/30 group-hover:border-blue-400/50'
                              }
                            `}>
                              {filters.isNew && (
                                <svg className="w-3 h-3 text-pure-black" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-lg">✨</span>
                            <span className={`text-sm font-medium ${
                              filters.isNew ? 'text-blue-400' : 'text-warm-camel group-hover:text-blue-400'
                            }`}>
                              Nuevos Lanzamientos
                            </span>
                          </div>
                        </motion.label>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Products Grid */}
          <div className="flex-1">
            {/* Results Info with Grid Layout Indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center justify-between mb-6"
            >
              <div className="flex items-center gap-4">
                <p className="text-warm-camel">
                  Mostrando {indexOfFirstProduct + 1}-{Math.min(indexOfLastProduct, filteredProducts.length)} de {filteredProducts.length} productos
                </p>
                {/* Enhanced Grid Layout Indicator (Development Mode) */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="hidden sm:flex items-center gap-3 text-xs">
                    {/* Current Breakpoint Indicator */}
                    <div className="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-lime-400/10 to-lime-600/10 border border-lime-400/20 rounded-lg">
                      <div className="w-2 h-2 bg-lime-400 rounded-full animate-pulse"></div>
                      <span className="text-lime-400 font-medium">
                        <span className="hidden 2xl:inline">2XL: 6 cols</span>
                        <span className="hidden xl:inline 2xl:hidden">XL: 5 cols</span>
                        <span className="hidden lg:inline xl:hidden">LG: 4 cols</span>
                        <span className="hidden md:inline lg:hidden">MD: 3 cols</span>
                        <span className="hidden sm:inline md:hidden">SM: 3 cols</span>
                        <span className="inline sm:hidden">XS: 2 cols</span>
                      </span>
                    </div>

                    {/* Sidebar Status */}
                    {showFilters && (
                      <div className="flex items-center gap-2 px-2 py-1 bg-gradient-to-r from-orange-400/10 to-red-500/10 border border-orange-400/20 rounded-lg">
                        <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                        <span className="text-orange-400 font-medium">Sidebar Active</span>
                      </div>
                    )}

                    {/* Grid Performance Indicator */}
                    <div className="flex items-center gap-2 px-2 py-1 bg-gradient-to-r from-blue-400/10 to-purple-500/10 border border-blue-400/20 rounded-lg">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span className="text-blue-400 font-medium">{currentProducts.length} items</span>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>

            {/* FIXED: Loading States with exact same grid as Nuestra Colección */}
            {isLoadingProducts ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className={`mb-8 ${
                  showFilters
                    ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                    : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6'
                }`}
              >
                {/* Luxury Loading Skeleton */}
                {[...Array(12)].map((_, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      duration: 0.3,
                      delay: index * 0.05,
                      ease: "easeOut"
                    }}
                    className="h-96 bg-gradient-to-br from-white/5 to-white/10 rounded-2xl border border-white/10 overflow-hidden relative"
                  >
                    {/* Shimmer Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>

                    {/* Image Skeleton */}
                    <div className="h-64 bg-gradient-to-br from-lime-400/10 to-lime-600/5 relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-lime-400/20 to-transparent animate-pulse"></div>
                    </div>

                    {/* Content Skeleton */}
                    <div className="p-4 space-y-3">
                      <div className="h-4 bg-white/10 rounded-lg animate-pulse"></div>
                      <div className="h-3 bg-white/5 rounded-lg w-3/4 animate-pulse"></div>
                      <div className="flex items-center justify-between">
                        <div className="h-6 bg-lime-400/20 rounded-lg w-20 animate-pulse"></div>
                        <div className="w-8 h-8 bg-lime-400/20 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              /* FIXED: Use exact same grid as Nuestra Colección section */
              <motion.div
                layout
                className={`mb-8 ${
                  showFilters
                    ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                    : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6'
                }`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <AnimatePresence mode="popLayout">
                  {currentProducts.map((product, index) => (
                    <motion.div
                      key={product.id}
                      className="h-full"
                      layout
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      transition={{
                        duration: 0.3,
                        delay: index * 0.05,
                        ease: "easeOut"
                      }}
                    >
                      <AnimatedProductCard
                        product={product}
                        index={index}
                        onAuthRequired={() => setIsAuthModalOpen(true)}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-center gap-2"
              >
                <Button
                  variant="secondary"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                >
                  Anterior
                </Button>
                
                {[...Array(totalPages)].map((_, index) => (
                  <Button
                    key={index + 1}
                    variant={currentPage === index + 1 ? 'primary' : 'ghost'}
                    onClick={() => setCurrentPage(index + 1)}
                  >
                    {index + 1}
                  </Button>
                ))}
                
                <Button
                  variant="secondary"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Siguiente
                </Button>
              </motion.div>
            )}

            {/* Error State */}
            {productsError && !isLoadingProducts && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <div className="text-6xl mb-4">⚠️</div>
                <h3 className="text-xl font-semibold text-red-400 mb-2">
                  {productsError.message}
                </h3>
                <p className="text-warm-camel mb-4">
                  {productsError.details}
                </p>
                {productsError.canRetry && (
                  <div className="flex gap-3 justify-center">
                    <Button variant="primary" onClick={retryLoadProducts}>
                      Reintentar
                    </Button>
                    <Button variant="secondary" onClick={clearFilters}>
                      Limpiar Filtros
                    </Button>
                  </div>
                )}
              </motion.div>
            )}

            {/* No Results */}
            {!isLoadingProducts && !productsError && filteredProducts.length === 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <div className="text-6xl mb-4">👟</div>
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-warm-camel mb-4">
                  Intenta ajustar tus filtros o buscar algo diferente
                </p>
                <div className="flex gap-3 justify-center">
                  <Button variant="primary" onClick={clearFilters}>
                    Limpiar Filtros
                  </Button>
                  <Button variant="secondary" onClick={retryLoadProducts}>
                    Recargar Productos
                  </Button>
                </div>
              </motion.div>
            )}

            {/* Enhanced Development Dashboard */}
            {process.env.NODE_ENV === 'development' && !isLoadingProducts && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-8 space-y-4"
              >
                {/* Data Source Status */}
                <div className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl">
                  <div className="text-xs text-blue-400 space-y-1">
                    <div>📊 <strong>Data Source Status:</strong></div>
                    <div>• Products loaded: {products.length}</div>
                    <div>• Filtered products: {filteredProducts.length}</div>
                    <div>• Current page: {currentPage}/{totalPages}</div>
                    <div>• Retry attempts: {retryCount}</div>
                    {productsError && <div className="text-red-400">• Error: {productsError.message}</div>}
                  </div>
                </div>

                {/* Grid Performance Metrics */}
                <div className="p-4 bg-gradient-to-r from-lime-500/10 to-green-500/10 border border-lime-500/20 rounded-xl">
                  <div className="text-xs text-lime-400 space-y-1">
                    <div>🎯 <strong>Grid Performance:</strong></div>
                    <div>• Render time: {gridPerformance.renderTime}ms</div>
                    <div>• Items rendered: {gridPerformance.itemCount}</div>
                    <div>• Current breakpoint: {gridPerformance.breakpoint}</div>
                    <div>• Grid mode: {showFilters ? 'Sidebar Active' : 'Full Width'}</div>
                    <div>• Performance: {gridPerformance.renderTime < 16 ? '🟢 Excellent' : gridPerformance.renderTime < 33 ? '🟡 Good' : '🔴 Needs Optimization'}</div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Floating AI Search Buttons */}
      <div className="fixed bottom-8 right-8 z-40 flex flex-col gap-3">
        <motion.button
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
          onClick={() => setIsVoiceSearchOpen(true)}
          className="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center hover:scale-110 group"
          title="Búsqueda por Voz"
        >
          <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
        </motion.button>

        <motion.button
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.6 }}
          onClick={() => setIsVisualSearchOpen(true)}
          className="w-14 h-14 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center hover:scale-110 group"
          title="Búsqueda Visual"
        >
          <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </motion.button>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      {/* AI Search Modals */}
      <EnhancedVoiceSearch
        isOpen={isVoiceSearchOpen}
        onClose={() => setIsVoiceSearchOpen(false)}
        onResults={handleVoiceSearchResults}
      />

      <VisualSearch
        isOpen={isVisualSearchOpen}
        onClose={() => setIsVisualSearchOpen(false)}
        onSearch={handleVisualSearch}
      />
    </div>
  )
}
