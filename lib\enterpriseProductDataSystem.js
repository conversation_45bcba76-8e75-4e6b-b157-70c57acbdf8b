// 🏗️ TWL ENTERPRISE PRODUCT DATA INTEGRATION SYSTEM
// 🎯 Mexican Market Pricing + Real Product Descriptions + CYTTE Architecture

/**
 * 💰 PRICING INTELLIGENCE SYSTEM
 * Based on supplier cost analysis from docs/Augment docs useful/Supplier cost analysis.md
 * 
 * Format from Description.txt: "💰[RMB] -- [USD]$"
 * Example: "💰300 -- 50$" = 300 RMB supplier cost, $50 USD converted
 * Transport: +$35 USD (China → Mexico)
 * 
 * PRICING TIERS (Mexican Market):
 * - Suggested: Total Cost × 2.5 (150% profit)
 * - Premium: Total Cost × 3.0 (200% profit) 
 * - Luxury: Total Cost × 4.0 (300% profit)
 */

export class TWLEnterpriseProductDataSystem {
  constructor() {
    this.TRANSPORT_COST_USD = 35; // China → Mexico transport
    this.EXCHANGE_RATES = {
      'RMB_TO_USD': 7.5, // Average rate from analysis
    };
    
    // Mexican market pricing strategy
    this.PRICING_TIERS = {
      SUGGESTED: 2.5,  // 150% profit margin
      PREMIUM: 3.0,    // 200% profit margin  
      LUXURY: 4.0      // 300% profit margin
    };
    
    // Category mapping for organized structure
    this.CATEGORY_MAPPING = {
      'sneakers': '1. SNEAKERS',
      'sandals': '2. SANDALS',
      'formal': '3. FORMAL',
      'casual': '4. CASUAL',
      'kids': '5. KIDS'
    };
    
    // Brand mapping for organized structure
    this.BRAND_MAPPING = {
      'nike': '1. NIKE Limited Edition',
      'gucci': '4. GUCCI',
      'dior': '5. DIOR',
      'lv': '6. LV',
      'chanel': '8. CHANEL',
      'balenciaga': '7. BALENCIAGA'
    };
  }

  /**
   * 📄 Parse Description.txt file content
   * Format: "💰[RMB] -- [USD]$\n[Product Name]\nTamaño:[sizes]\nID:[sku]"
   */
  parseDescriptionFile(content) {
    const lines = content.split('\n').filter(line => line.trim());
    
    if (lines.length < 3) {
      console.warn('Invalid description file format');
      return null;
    }
    
    // Parse pricing line: "💰300 -- 50$"
    const pricingLine = lines.find(line => line.includes('💰'));
    let supplierCostRMB = 0;
    let supplierCostUSD = 0;
    
    if (pricingLine) {
      const pricingMatch = pricingLine.match(/💰(\d+)\s*--\s*(\d+)\$/);
      if (pricingMatch) {
        supplierCostRMB = parseInt(pricingMatch[1]);
        supplierCostUSD = parseInt(pricingMatch[2]);
      }
    }
    
    // Parse product name (usually line 2)
    const productName = lines.find(line => 
      !line.includes('💰') && 
      !line.includes('Tamaño') && 
      !line.includes('ID:') &&
      !line.includes('#') &&
      line.length > 10
    ) || '';
    
    // Parse sizes: "Tamaño:39-44" or "Tamaño: 35 36 37 38 39 40 41 42 43 44 45"
    const sizeLine = lines.find(line => line.toLowerCase().includes('tamaño'));
    let availableSizes = [];
    
    if (sizeLine) {
      const sizeMatch = sizeLine.match(/tamaño:?\s*(.+)/i);
      if (sizeMatch) {
        const sizeStr = sizeMatch[1].trim();
        if (sizeStr.includes('-')) {
          // Range format: "39-44"
          const [start, end] = sizeStr.split('-').map(s => parseInt(s.trim()));
          for (let i = start; i <= end; i++) {
            availableSizes.push(i);
          }
        } else {
          // Space-separated format: "35 36 37 38 39 40 41 42 43 44 45"
          availableSizes = sizeStr.split(/\s+/).map(s => parseInt(s)).filter(s => !isNaN(s));
        }
      }
    }
    
    // Parse SKU/ID
    const idLine = lines.find(line => line.includes('ID:') || line.includes('#'));
    let sku = '';
    
    if (idLine) {
      const idMatch = idLine.match(/(?:ID:|#)(.+)/);
      if (idMatch) {
        sku = idMatch[1].trim();
      }
    }
    
    return {
      supplierCostRMB,
      supplierCostUSD,
      productName: productName.trim(),
      availableSizes,
      sku: sku || 'UNKNOWN'
    };
  }

  /**
   * 💰 Calculate Mexican market pricing based on supplier costs
   */
  calculateMexicanPricing(supplierCostUSD) {
    const totalCost = supplierCostUSD + this.TRANSPORT_COST_USD;
    
    return {
      // Backend-only data (never shown to customers)
      backend: {
        supplierCost: supplierCostUSD,
        transportCost: this.TRANSPORT_COST_USD,
        totalCost: totalCost,
        confidential: true
      },
      
      // Frontend pricing (shown to customers)
      pricing: {
        suggested: Math.round(totalCost * this.PRICING_TIERS.SUGGESTED),
        premium: Math.round(totalCost * this.PRICING_TIERS.PREMIUM),
        luxury: Math.round(totalCost * this.PRICING_TIERS.LUXURY),
        currency: 'USD'
      },
      
      // Profit margins
      margins: {
        suggested: Math.round((this.PRICING_TIERS.SUGGESTED - 1) * 100), // 150%
        premium: Math.round((this.PRICING_TIERS.PREMIUM - 1) * 100),     // 200%
        luxury: Math.round((this.PRICING_TIERS.LUXURY - 1) * 100)        // 300%
      }
    };
  }

  /**
   * 🏷️ Create enterprise product object with Mexican market data
   */
  createEnterpriseProduct(descriptionData, category, brand, gender = 'mixte') {
    const pricing = this.calculateMexicanPricing(descriptionData.supplierCostUSD);
    
    // Generate product ID based on CYTTE structure
    const productId = `${category}-${brand}-${gender}-${descriptionData.sku}`.toLowerCase();
    
    return {
      // Core product data
      id: productId,
      sku: descriptionData.sku,
      name: this.cleanProductName(descriptionData.productName),
      brand: this.cleanProductName(brand.charAt(0).toUpperCase() + brand.slice(1)),
      category: category,
      gender: gender,
      
      // Mexican market description (Spanish)
      description: descriptionData.productName,
      
      // Pricing (frontend - customers see this)
      price: pricing.pricing.suggested,        // Default to suggested pricing
      originalPrice: pricing.pricing.luxury,   // Show luxury as "original" for discount effect
      currency: 'USD',
      
      // Sizing information
      sizing: {
        availableSizes: descriptionData.availableSizes,
        sizeChart: 'european',
        estimated: false
      },
      
      // Product status
      inStock: true,
      isNew: false,
      isVip: pricing.pricing.suggested > 300, // VIP if over $300
      limitedEdition: descriptionData.productName.toLowerCase().includes('limited'),
      
      // Search optimization
      searchKeywords: this.generateSearchKeywords(descriptionData.productName, brand, category),
      
      // Media paths (organized structure)
      media: {
        images: this.generateImagePaths(category, brand, gender, descriptionData.sku),
        videos: this.generateVideoPaths(category, brand, gender, descriptionData.sku)
      },
      
      // Backend data (NEVER shown to customers)
      _backend: pricing.backend,
      _pricingTiers: pricing.pricing,
      _margins: pricing.margins,
      
      // Metadata
      dataSource: {
        extractedFrom: 'Description.txt',
        lastUpdated: new Date().toISOString(),
        version: '2.0'
      }
    };
  }

  /**
   * 🧹 Clean product name (remove folder reference numbers)
   */
  cleanProductName(name) {
    if (!name) return 'Product Name';
    return name.replace(/^\d+\.\s*/, '').trim();
  }

  /**
   * 🔍 Generate search keywords for Mexican market
   */
  generateSearchKeywords(productName, brand, category) {
    const keywords = [];
    
    // Brand keywords
    keywords.push(brand.toLowerCase());
    
    // Category keywords
    keywords.push(category);
    if (category === 'sneakers') {
      keywords.push('tenis', 'zapatillas', 'deportivos');
    } else if (category === 'sandals') {
      keywords.push('sandalias', 'chanclas');
    }
    
    // Extract keywords from product name
    const nameWords = productName.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter(word => !['de', 'la', 'el', 'con', 'para', 'por'].includes(word));
    
    keywords.push(...nameWords);
    
    // Mexican market specific keywords
    keywords.push('lujo', 'premium', 'exclusivo', 'moda');
    
    return [...new Set(keywords)]; // Remove duplicates
  }

  /**
   * 🖼️ Generate image paths for organized structure
   */
  generateImagePaths(category, brand, gender, sku) {
    const basePath = `products-organized/${this.getCategoryNumber(category)}-${category}`;
    const brandPath = `${this.getBrandNumber(brand)}-${brand}`;
    const productPath = `${sku}`;
    
    // Generate paths for multiple images
    const imagePaths = [];
    for (let i = 1; i <= 36; i++) {
      imagePaths.push(`${basePath}/${brandPath}/1-${brand}/1-${gender}/${productPath}/image-${i}.webp`);
    }
    
    return imagePaths;
  }

  /**
   * 🎥 Generate video paths for organized structure
   */
  generateVideoPaths(category, brand, gender, sku) {
    const basePath = `products-organized/${this.getCategoryNumber(category)}-${category}`;
    const brandPath = `${this.getBrandNumber(brand)}-${brand}`;
    const productPath = `${sku}`;
    
    return [
      `${basePath}/${brandPath}/1-${brand}/1-${gender}/${productPath}/${sku}-mobile.mp4`,
      `${basePath}/${brandPath}/1-${brand}/1-${gender}/${productPath}/${sku}-desktop.mp4`
    ];
  }

  /**
   * 🔢 Get category number for organized structure
   */
  getCategoryNumber(category) {
    const mapping = {
      'sneakers': '1',
      'sandals': '2', 
      'formal': '3',
      'casual': '4',
      'kids': '5'
    };
    return mapping[category] || '1';
  }

  /**
   * 🔢 Get brand number for organized structure
   */
  getBrandNumber(brand) {
    const mapping = {
      'nike': '1',
      'adidas': '2',
      'hermes': '3',
      'gucci': '4',
      'dior': '5',
      'lv': '6',
      'balenciaga': '7',
      'chanel': '8'
    };
    return mapping[brand.toLowerCase()] || '1';
  }

  /**
   * 📊 Load product from description file path
   */
  async loadProductFromPath(filePath) {
    try {
      const response = await fetch(filePath);
      if (!response.ok) {
        throw new Error(`Failed to load ${filePath}`);
      }
      
      const content = await response.text();
      const descriptionData = this.parseDescriptionFile(content);
      
      if (!descriptionData) {
        throw new Error('Invalid description file format');
      }
      
      // Extract category, brand, gender from file path
      const pathParts = filePath.split('/');
      const category = this.extractCategoryFromPath(pathParts);
      const brand = this.extractBrandFromPath(pathParts);
      const gender = this.extractGenderFromPath(pathParts);
      
      return this.createEnterpriseProduct(descriptionData, category, brand, gender);
      
    } catch (error) {
      console.error('Error loading product:', error);
      return null;
    }
  }

  /**
   * 🗂️ Extract category from file path
   */
  extractCategoryFromPath(pathParts) {
    const categoryPart = pathParts.find(part => part.includes('SNEAKERS') || part.includes('SANDALS') || part.includes('FORMAL') || part.includes('CASUAL') || part.includes('KIDS'));
    
    if (categoryPart?.includes('SNEAKERS')) return 'sneakers';
    if (categoryPart?.includes('SANDALS')) return 'sandals';
    if (categoryPart?.includes('FORMAL')) return 'formal';
    if (categoryPart?.includes('CASUAL')) return 'casual';
    if (categoryPart?.includes('KIDS')) return 'kids';
    
    return 'sneakers'; // Default
  }

  /**
   * 🏷️ Extract brand from file path
   */
  extractBrandFromPath(pathParts) {
    const brandPart = pathParts.find(part => 
      part.includes('GUCCI') || 
      part.includes('NIKE') || 
      part.includes('DIOR') || 
      part.includes('LV') || 
      part.includes('CHANEL') || 
      part.includes('BALENCIAGA')
    );
    
    if (brandPart?.includes('GUCCI')) return 'gucci';
    if (brandPart?.includes('NIKE')) return 'nike';
    if (brandPart?.includes('DIOR')) return 'dior';
    if (brandPart?.includes('LV')) return 'lv';
    if (brandPart?.includes('CHANEL')) return 'chanel';
    if (brandPart?.includes('BALENCIAGA')) return 'balenciaga';
    
    return 'nike'; // Default
  }

  /**
   * 👥 Extract gender from file path
   */
  extractGenderFromPath(pathParts) {
    const genderPart = pathParts.find(part => 
      part.includes('MIXTE') || 
      part.includes('WOMEN') || 
      part.includes('MEN')
    );
    
    if (genderPart?.includes('WOMEN')) return 'women';
    if (genderPart?.includes('MEN')) return 'men';
    
    return 'mixte'; // Default to unisex
  }

  /**
   * 🚀 Batch process multiple products from directory structure
   */
  async batchProcessProducts(productPaths) {
    const results = {
      success: [],
      failed: [],
      total: productPaths.length
    };

    console.log(`🚀 Starting batch processing of ${productPaths.length} products...`);

    for (let i = 0; i < productPaths.length; i++) {
      const path = productPaths[i];
      console.log(`📦 Processing ${i + 1}/${productPaths.length}: ${path}`);

      try {
        const product = await this.loadProductFromPath(path);
        if (product) {
          results.success.push(product);
          console.log(`✅ Successfully processed: ${product.name}`);
        } else {
          results.failed.push({ path, error: 'Failed to create product' });
          console.log(`❌ Failed to process: ${path}`);
        }
      } catch (error) {
        results.failed.push({ path, error: error.message });
        console.log(`❌ Error processing ${path}:`, error.message);
      }

      // Add small delay to prevent overwhelming the system
      if (i < productPaths.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`🎉 Batch processing complete! Success: ${results.success.length}, Failed: ${results.failed.length}`);
    return results;
  }

  /**
   * 💾 Export products to JSON for database import
   */
  exportToJSON(products) {
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalProducts: products.length,
        version: '2.0',
        source: 'TWL Enterprise Product Data System'
      },
      products: products.map(product => ({
        ...product,
        // Remove backend data from export
        _backend: undefined,
        _pricingTiers: undefined,
        _margins: undefined
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 📊 Generate analytics report
   */
  generateAnalyticsReport(products) {
    const report = {
      overview: {
        totalProducts: products.length,
        categories: {},
        brands: {},
        priceRanges: {
          budget: 0,    // < $100
          mid: 0,       // $100-300
          premium: 0,   // $300-500
          luxury: 0     // > $500
        }
      },
      pricing: {
        averagePrice: 0,
        medianPrice: 0,
        minPrice: Infinity,
        maxPrice: 0
      },
      inventory: {
        inStock: 0,
        outOfStock: 0,
        limitedEdition: 0,
        vipProducts: 0
      }
    };

    // Calculate analytics
    const prices = [];

    products.forEach(product => {
      // Categories
      report.overview.categories[product.category] =
        (report.overview.categories[product.category] || 0) + 1;

      // Brands
      report.overview.brands[product.brand] =
        (report.overview.brands[product.brand] || 0) + 1;

      // Price ranges
      if (product.price < 100) report.overview.priceRanges.budget++;
      else if (product.price < 300) report.overview.priceRanges.mid++;
      else if (product.price < 500) report.overview.priceRanges.premium++;
      else report.overview.priceRanges.luxury++;

      // Pricing stats
      prices.push(product.price);
      report.pricing.minPrice = Math.min(report.pricing.minPrice, product.price);
      report.pricing.maxPrice = Math.max(report.pricing.maxPrice, product.price);

      // Inventory stats
      if (product.inStock) report.inventory.inStock++;
      else report.inventory.outOfStock++;

      if (product.limitedEdition) report.inventory.limitedEdition++;
      if (product.isVip) report.inventory.vipProducts++;
    });

    // Calculate average and median
    report.pricing.averagePrice = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length);
    prices.sort((a, b) => a - b);
    const mid = Math.floor(prices.length / 2);
    report.pricing.medianPrice = prices.length % 2 === 0
      ? Math.round((prices[mid - 1] + prices[mid]) / 2)
      : prices[mid];

    return report;
  }
}

// Export singleton instance
export const enterpriseProductSystem = new TWLEnterpriseProductDataSystem();

// Export pricing utilities for components
export const PricingUtils = {
  formatPrice: (price, currency = 'USD') => {
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(price);
  },
  
  calculateDiscount: (originalPrice, currentPrice) => {
    return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
  },
  
  getPriceRange: (supplierCost) => {
    const system = new TWLEnterpriseProductDataSystem();
    const pricing = system.calculateMexicanPricing(supplierCost);
    return {
      min: pricing.pricing.suggested,
      max: pricing.pricing.luxury
    };
  }
};
